import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Link,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  FamilyRestroom as FamilyIcon,
  LocationOn as LocationIcon,
  Badge as BadgeIcon,
  Biotech as DnaIcon,
  Timeline as TimelineIcon,
  AccountTree as TreeIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { analyticsApi, IndividualDetailsResponse, Individual, Tree, DNAMatch, SharedSegment, IndividualFamily, Surname, Place } from '../../services/api';

const IndividualDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Progressive loading: Load basic info first, then other sections
  const {
    data: basicInfo,
    isLoading: basicLoading,
    error: basicError,
    refetch: refetchBasic,
  } = useQuery({
    queryKey: ['individualBasic', id],
    queryFn: () => analyticsApi.getIndividualBasicInfo(id!),
    enabled: !!id,
    refetchOnWindowFocus: false,
  });

  const {
    data: dnaMatches,
    isLoading: dnaLoading,
    error: dnaError,
    refetch: refetchDNA,
  } = useQuery({
    queryKey: ['individualDNA', id],
    queryFn: () => analyticsApi.getIndividualDNAMatches(id!),
    enabled: !!id,
    refetchOnWindowFocus: false,
  });

  const {
    data: sharedSegments,
    isLoading: segmentsLoading,
    error: segmentsError,
    refetch: refetchSegments,
  } = useQuery({
    queryKey: ['individualSegments', id],
    queryFn: () => analyticsApi.getIndividualSharedSegments(id!),
    enabled: !!id,
    refetchOnWindowFocus: false,
  });

  const {
    data: familyData,
    isLoading: familyLoading,
    error: familyError,
    refetch: refetchFamily,
  } = useQuery({
    queryKey: ['individualFamily', id],
    queryFn: () => analyticsApi.getIndividualFamily(id!),
    enabled: !!id,
    refetchOnWindowFocus: false,
  });

  const {
    data: surnamesData,
    isLoading: surnamesLoading,
    error: surnamesError,
    refetch: refetchSurnames,
  } = useQuery({
    queryKey: ['individualSurnames', id],
    queryFn: () => analyticsApi.getIndividualSurnames(id!),
    enabled: !!id,
    refetchOnWindowFocus: false,
  });

  const {
    data: placesData,
    isLoading: placesLoading,
    error: placesError,
    refetch: refetchPlaces,
  } = useQuery({
    queryKey: ['individualPlaces', id],
    queryFn: () => analyticsApi.getIndividualPlaces(id!),
    enabled: !!id,
    refetchOnWindowFocus: false,
  });

  const handleBack = () => {
    navigate('/individuals');
  };

  const handleRefresh = () => {
    refetchBasic();
    refetchDNA();
    refetchSegments();
    refetchFamily();
    refetchSurnames();
    refetchPlaces();
  };

  // Show loading only for basic info (most important)
  if (basicLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  // Show error only for basic info (most important)
  if (basicError) {
    return (
      <Alert
        severity="error"
        action={
          <Button onClick={handleRefresh} color="error" variant="outlined">
            Retry
          </Button>
        }
      >
        Failed to load individual details. Please check if the API server is running.
      </Alert>
    );
  }

  if (!basicInfo) {
    return (
      <Alert severity="warning">
        Individual not found.
      </Alert>
    );
  }

  // Extract data from progressive loading queries
  const { individual, tree } = basicInfo;
  const dna_matches = dnaMatches?.dna_matches || [];
  const shared_segments = sharedSegments?.shared_segments || [];
  const family_relationships = familyData?.family_relationships || [];
  const surnames = surnamesData?.surnames || [];
  const places = placesData?.places || [];

  // Combine stats from all sections
  const stats = {
    total_matches: dnaMatches?.stats?.total_matches || 0,
    total_segments: sharedSegments?.stats?.total_segments || 0,
    total_surnames: surnamesData?.stats?.total_surnames || 0,
    total_places: placesData?.stats?.total_places || 0,
    total_family_roles: familyData?.stats?.total_family_roles || 0,
  };

  return (
    <Box>
      {/* Header */}
      <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
        <Box display="flex" alignItems="center" gap={2}>
          <IconButton onClick={handleBack} color="primary">
            <ArrowBackIcon />
          </IconButton>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
              {individual.Name || 'Unknown Individual'}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Individual ID: {individual.ID}
            </Typography>
          </Box>
        </Box>
        <Tooltip title="Refresh Data">
          <IconButton onClick={handleRefresh} color="primary">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Statistics Overview */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <DnaIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">DNA Matches</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {stats.total_matches}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TimelineIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Segments</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {stats.total_segments}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <BadgeIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Surnames</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {stats.total_surnames}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <LocationIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Places</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {stats.total_places}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <FamilyIcon color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6">Family Roles</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {stats.total_family_roles}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <PersonIcon sx={{ mr: 1 }} />
              Basic Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">Full Name</Typography>
                <Typography variant="body1">{individual.Name || 'Unknown'}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Gender</Typography>
                <Typography variant="body1">{individual.Gender || 'Unknown'}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Birth Year</Typography>
                <Typography variant="body1">{individual.BirthDateYear || 'Unknown'}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Death Year</Typography>
                <Typography variant="body1">{individual.DeathDateYear || 'Unknown'}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Age Group</Typography>
                <Typography variant="body1">{individual.AgeGroupInYears || 'Unknown'}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">Birth Place</Typography>
                <Typography variant="body1">{individual.BirthPlace || 'Unknown'}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">Relationship Description</Typography>
                <Typography variant="body1">{individual.RelationshipDescription || 'Unknown'}</Typography>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Tree Information */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <TreeIcon sx={{ mr: 1 }} />
              Family Tree
            </Typography>
            <Divider sx={{ mb: 2 }} />
            {tree ? (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">Tree Name</Typography>
                  <Typography variant="body1">{tree.Name}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Individual Count</Typography>
                  <Typography variant="body1">{tree.IndividualCount?.toLocaleString() || 'Unknown'}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Creator</Typography>
                  <Typography variant="body1">{tree.SiteCreatorName || 'Unknown'}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">Creator Country</Typography>
                  <Typography variant="body1">{tree.SiteCreatorCountry || 'Unknown'}</Typography>
                </Grid>
                {(individual.LinkInTree || individual.LinkInPedigreeTree) && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>Tree Links</Typography>
                    <Box display="flex" gap={1} flexWrap="wrap">
                      {individual.LinkInTree && (
                        <Button
                          variant="outlined"
                          size="small"
                          href={individual.LinkInTree}
                          target="_blank"
                          rel="noopener noreferrer"
                          startIcon={<TreeIcon />}
                        >
                          View in Family Tree
                        </Button>
                      )}
                      {individual.LinkInPedigreeTree && (
                        <Button
                          variant="outlined"
                          size="small"
                          href={individual.LinkInPedigreeTree}
                          target="_blank"
                          rel="noopener noreferrer"
                          startIcon={<TreeIcon />}
                        >
                          View in Pedigree Tree
                        </Button>
                      )}
                    </Box>
                  </Grid>
                )}
              </Grid>
            ) : (
              <Typography variant="body1" color="text.secondary">
                No family tree associated with this individual.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Surnames */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <BadgeIcon sx={{ mr: 1 }} />
              Associated Surnames ({surnames?.length || 0})
              {surnamesLoading && <CircularProgress size={16} sx={{ ml: 1 }} />}
            </Typography>
            <Divider sx={{ mb: 2 }} />
            {surnamesLoading ? (
              <Box display="flex" justifyContent="center" alignItems="center" minHeight="60px">
                <CircularProgress size={24} />
              </Box>
            ) : surnamesError ? (
              <Alert severity="error">
                Failed to load surnames
              </Alert>
            ) : surnames?.length > 0 ? (
              <Box display="flex" flexWrap="wrap" gap={1}>
                {surnames.map((surname: any, index: number) => (
                  <Chip
                    key={`${surname.ID}-${index}`}
                    label={surname.Surname}
                    variant="outlined"
                    size="small"
                    clickable
                    onClick={() => navigate(`/surnames/${surname.ID}`)}
                    sx={{ cursor: 'pointer' }}
                  />
                ))}
              </Box>
            ) : (
              <Typography variant="body1" color="text.secondary">
                No surnames associated with this individual.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Places */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <LocationIcon sx={{ mr: 1 }} />
              Associated Places ({places?.length || 0})
            </Typography>
            <Divider sx={{ mb: 2 }} />
            {places?.length > 0 ? (
              <List dense>
                {places.map((place) => (
                  <ListItem
                    key={place.ID}
                    disablePadding
                    onClick={() => navigate(`/places/${place.ID}`)}
                    sx={{ cursor: 'pointer', '&:hover': { backgroundColor: 'action.hover' } }}
                  >
                    <ListItemIcon>
                      <LocationIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary={`${place.Country || 'Unknown Country'}${place.StateOrProvince ? `, ${place.StateOrProvince}` : ''}`}
                      secondary={`${place.CountryCode || ''}${place.StateOrProvinceCode ? `, ${place.StateOrProvinceCode}` : ''}`}
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body1" color="text.secondary">
                No places associated with this individual.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Family Relationships */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <FamilyIcon sx={{ mr: 1 }} />
              Family Relationships ({family_relationships?.length || 0})
            </Typography>
            <Divider sx={{ mb: 2 }} />
            {family_relationships?.length > 0 ? (
              <List dense>
                {family_relationships.map((relationship) => (
                  <ListItem key={relationship.ID} disablePadding>
                    <ListItemIcon>
                      <FamilyIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary={relationship.Role}
                      secondary={`Family ID: ${relationship.FamilyID}`}
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body1" color="text.secondary">
                No family relationships recorded for this individual.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* DNA Matches */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <DnaIcon sx={{ mr: 1 }} />
              DNA Matches ({dna_matches?.length || 0})
              {dnaLoading && <CircularProgress size={16} sx={{ ml: 1 }} />}
            </Typography>
            <Divider sx={{ mb: 2 }} />
            {dnaLoading ? (
              <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
                <Box textAlign="center">
                  <CircularProgress size={40} />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    Loading DNA matches... This may take up to 10 seconds.
                  </Typography>
                </Box>
              </Box>
            ) : dnaError ? (
              <Alert severity="error">
                Failed to load DNA matches. This section requires more processing time.
              </Alert>
            ) : dna_matches?.length > 0 ? (
              <TableContainer sx={{ maxHeight: 400 }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Matched Individual</TableCell>
                      <TableCell>Shared cM</TableCell>
                      <TableCell>Percentage</TableCell>
                      <TableCell>Segments</TableCell>
                      <TableCell>Confidence</TableCell>
                      <TableCell>Relationship</TableCell>
                      <TableCell>Created</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {dna_matches.slice(0, 50).map((match) => (
                      <TableRow key={match.ID} hover>
                        <TableCell>
                          {match.matched_individual_name && match.matched_individual_id ? (
                            <Link
                              component="button"
                              variant="body2"
                              onClick={() => navigate(`/individuals/${match.matched_individual_id}`)}
                              sx={{
                                textAlign: 'left',
                                cursor: 'pointer',
                                '&:hover': { textDecoration: 'underline' }
                              }}
                            >
                              {match.matched_individual_name}
                            </Link>
                          ) : (
                            <Typography variant="body2" color="text.secondary">
                              {match.matched_individual_id || 'Unknown Individual'}
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell>{match.TotalSharedSegmentsLengthInCm?.toFixed(2) || 'Unknown'}</TableCell>
                        <TableCell>{match.PercentageOfSharedSegments?.toFixed(3) || 'Unknown'}%</TableCell>
                        <TableCell>{match.TotalSharedSegments || 0}</TableCell>
                        <TableCell>
                          <Chip
                            label={match.ConfidenceLevel || 'Unknown'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>{match.ExactDnaRelationship || 'Unknown'}</TableCell>
                        <TableCell>{new Date(match.CreatedAt).toLocaleDateString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body1" color="text.secondary">
                No DNA matches found for this individual.
              </Typography>
            )}
            {dna_matches?.length > 50 && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Showing first 50 matches out of {dna_matches?.length || 0} total matches.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Shared Segments */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom display="flex" alignItems="center">
              <TimelineIcon sx={{ mr: 1 }} />
              Shared DNA Segments ({shared_segments?.length || 0})
            </Typography>
            <Divider sx={{ mb: 2 }} />
            {shared_segments?.length > 0 ? (
              <TableContainer sx={{ maxHeight: 400 }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Match ID</TableCell>
                      <TableCell>Chromosome</TableCell>
                      <TableCell>Start Position</TableCell>
                      <TableCell>End Position</TableCell>
                      <TableCell>Length (cM)</TableCell>
                      <TableCell>SNP Count</TableCell>
                      <TableCell>Start RSID</TableCell>
                      <TableCell>End RSID</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {shared_segments.slice(0, 100).map((segment) => (
                      <TableRow key={segment.ID} hover>
                        <TableCell>{segment.MatchID}</TableCell>
                        <TableCell>{segment.ChromosomeID}</TableCell>
                        <TableCell>{segment.StartPosition?.toLocaleString() || 'Unknown'}</TableCell>
                        <TableCell>{segment.EndPosition?.toLocaleString() || 'Unknown'}</TableCell>
                        <TableCell>{segment.LengthInCentimorgans?.toFixed(2) || 'Unknown'}</TableCell>
                        <TableCell>{segment.SnpCount?.toLocaleString() || 'Unknown'}</TableCell>
                        <TableCell>{segment.StartRsid}</TableCell>
                        <TableCell>{segment.EndRsid}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body1" color="text.secondary">
                No shared DNA segments found for this individual.
              </Typography>
            )}
            {shared_segments?.length > 100 && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Showing first 100 segments out of {shared_segments?.length || 0} total segments.
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default IndividualDetail;
