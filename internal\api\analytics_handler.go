package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"time"

	"github.com/dmagur/myheritage/internal/database"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AnalyticsHandler handles API requests for analytics
type AnalyticsHandler struct {
	db     *gorm.DB
	logger *log.Logger
}

// NewAnalyticsHandler creates a new AnalyticsHandler
func NewAnalyticsHandler(db *gorm.DB, logger *log.Logger) *AnalyticsHandler {
	return &AnalyticsHandler{
		db:     db,
		logger: logger,
	}
}

// RegisterRoutes registers the analytics routes
func (h *AnalyticsHandler) RegisterRoutes(router *gin.Engine) {
	// Group API routes under /api/v1/analytics
	analytics := router.Group("/api/v1/analytics")
	{
		analytics.GET("/overview", h.GetOverview)
		analytics.GET("/matches/stats", h.GetMatchStats)
		analytics.GET("/geography/top-countries", h.GetTopCountries)
		analytics.GET("/individuals/stats", h.<PERSON>IndividualStats)

		// Network visualization endpoints
		analytics.GET("/network/graph", h.GetNetworkGraph)
		analytics.GET("/network/cliques", h.GetNetworkCliques)

		// Enhanced data endpoints with filtering
		analytics.GET("/matches", h.GetMatches)
		analytics.GET("/individuals", h.GetIndividuals)
		analytics.GET("/individuals/:id", h.GetIndividualDetails)

		// Individual detail sub-endpoints for progressive loading
		analytics.GET("/individuals/:id/basic", h.GetIndividualBasicInfo)
		analytics.GET("/individuals/:id/dna-matches", h.GetIndividualDNAMatches)
		analytics.GET("/individuals/:id/shared-segments", h.GetIndividualSharedSegments)
		analytics.GET("/individuals/:id/family", h.GetIndividualFamily)
		analytics.GET("/individuals/:id/surnames", h.GetIndividualSurnames)
		analytics.GET("/individuals/:id/places", h.GetIndividualPlaces)

		analytics.GET("/surnames/:id", h.GetSurnameDetails)
		analytics.GET("/places/:id", h.GetPlaceDetails)
		analytics.GET("/segments", h.GetSegments)
	}
}

// GetOverview returns database overview statistics
func (h *AnalyticsHandler) GetOverview(c *gin.Context) {
	h.logger.Info("Getting database overview statistics")

	var overview OverviewResponse
	overview.LastUpdated = time.Now()

	// Use a single optimized query to get all counts at once
	// This is much faster than multiple separate COUNT queries
	var result struct {
		DNAMatches     int64 `json:"dna_matches"`
		Individuals    int64 `json:"individuals"`
		Trees          int64 `json:"trees"`
		SharedSegments int64 `json:"shared_segments"`
		Submitters     int64 `json:"submitters"`
		Families       int64 `json:"families"`
		Surnames       int64 `json:"surnames"`
	}

	// Execute optimized query with timeout
	ctx := c.Request.Context()
	query := `
		SELECT
			(SELECT COUNT(*) FROM dna_matches) as dna_matches,
			(SELECT COUNT(*) FROM individuals) as individuals,
			(SELECT COUNT(*) FROM trees) as trees,
			(SELECT COUNT(*) FROM shared_segment) as shared_segments,
			(SELECT COUNT(*) FROM submitters) as submitters,
			(SELECT COUNT(*) FROM family) as families,
			(SELECT COUNT(*) FROM surname) as surnames
	`

	if err := h.db.WithContext(ctx).Raw(query).Scan(&result).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get overview statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get overview statistics"})
		return
	}

	overview.TotalDNAMatches = result.DNAMatches
	overview.TotalIndividuals = result.Individuals
	overview.TotalTrees = result.Trees
	overview.TotalSharedSegments = result.SharedSegments
	overview.TotalSubmitters = result.Submitters
	overview.TotalFamilies = result.Families
	overview.TotalSurnames = result.Surnames

	h.logger.WithFields(log.Fields{
		"dna_matches":     overview.TotalDNAMatches,
		"individuals":     overview.TotalIndividuals,
		"trees":           overview.TotalTrees,
		"shared_segments": overview.TotalSharedSegments,
		"submitters":      overview.TotalSubmitters,
		"families":        overview.TotalFamilies,
		"surnames":        overview.TotalSurnames,
	}).Info("Database overview statistics retrieved")

	c.JSON(http.StatusOK, overview)
}

// GetMatchStats returns DNA match statistics
func (h *AnalyticsHandler) GetMatchStats(c *gin.Context) {
	h.logger.Info("Getting DNA match statistics")

	var stats MatchStatsResponse
	ctx := c.Request.Context()

	// Get basic statistics in a single optimized query
	var basicStats struct {
		TotalMatches int64   `json:"total_matches"`
		AvgCM        float64 `json:"avg_cm"`
		MaxCM        float64 `json:"max_cm"`
		MinCM        float64 `json:"min_cm"`
	}

	basicQuery := `
		SELECT
			COUNT(*) as total_matches,
			AVG(CASE WHEN total_shared_segments_length_in_cm > 0 THEN total_shared_segments_length_in_cm END) as avg_cm,
			MAX(total_shared_segments_length_in_cm) as max_cm,
			MIN(CASE WHEN total_shared_segments_length_in_cm > 0 THEN total_shared_segments_length_in_cm END) as min_cm
		FROM dna_matches
	`

	if err := h.db.WithContext(ctx).Raw(basicQuery).Scan(&basicStats).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get basic match statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get basic match statistics"})
		return
	}

	stats.TotalMatches = basicStats.TotalMatches
	stats.AvgSharedCM = basicStats.AvgCM
	stats.MaxSharedCM = basicStats.MaxCM
	stats.MinSharedCM = basicStats.MinCM

	// Get confidence level distribution with timeout
	var confidenceResults []struct {
		ConfidenceLevel string
		Count           int64
	}

	if err := h.db.WithContext(ctx).Model(&database.DNAMatch{}).
		Select("COALESCE(confidence_level, 'unknown') as confidence_level, COUNT(*) as count").
		Group("confidence_level").
		Limit(20).
		Scan(&confidenceResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get confidence distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get confidence distribution"})
		return
	}

	stats.MatchesByConfidence = make(map[string]int64)
	for _, result := range confidenceResults {
		stats.MatchesByConfidence[result.ConfidenceLevel] = result.Count
	}

	// Get relationship distribution with timeout and limit
	var relationshipResults []struct {
		ExactDnaRelationship string
		Count                int64
	}

	if err := h.db.WithContext(ctx).Model(&database.DNAMatch{}).
		Select("COALESCE(exact_dna_relationship, 'unknown') as exact_dna_relationship, COUNT(*) as count").
		Group("exact_dna_relationship").
		Order("count DESC").
		Limit(15).
		Scan(&relationshipResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get relationship distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get relationship distribution"})
		return
	}

	stats.MatchesByRelationship = make(map[string]int64)
	for _, result := range relationshipResults {
		stats.MatchesByRelationship[result.ExactDnaRelationship] = result.Count
	}

	// Get cM distribution using a single optimized query
	distributionQuery := `
		SELECT
			CASE
				WHEN total_shared_segments_length_in_cm >= 0 AND total_shared_segments_length_in_cm < 10 THEN '0-10'
				WHEN total_shared_segments_length_in_cm >= 10 AND total_shared_segments_length_in_cm < 20 THEN '10-20'
				WHEN total_shared_segments_length_in_cm >= 20 AND total_shared_segments_length_in_cm < 50 THEN '20-50'
				WHEN total_shared_segments_length_in_cm >= 50 AND total_shared_segments_length_in_cm < 100 THEN '50-100'
				WHEN total_shared_segments_length_in_cm >= 100 AND total_shared_segments_length_in_cm < 200 THEN '100-200'
				WHEN total_shared_segments_length_in_cm >= 200 AND total_shared_segments_length_in_cm < 500 THEN '200-500'
				WHEN total_shared_segments_length_in_cm >= 500 AND total_shared_segments_length_in_cm < 1000 THEN '500-1000'
				WHEN total_shared_segments_length_in_cm >= 1000 THEN '1000+'
				ELSE 'unknown'
			END as bucket,
			COUNT(*) as count
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= 0
		GROUP BY bucket
		ORDER BY
			CASE bucket
				WHEN '0-10' THEN 1
				WHEN '10-20' THEN 2
				WHEN '20-50' THEN 3
				WHEN '50-100' THEN 4
				WHEN '100-200' THEN 5
				WHEN '200-500' THEN 6
				WHEN '500-1000' THEN 7
				WHEN '1000+' THEN 8
				ELSE 9
			END
	`

	var distributionResults []struct {
		Bucket string
		Count  int64
	}

	if err := h.db.WithContext(ctx).Raw(distributionQuery).Scan(&distributionResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get cM distribution")
		// Don't fail the entire request, just log the error
		stats.CMDistribution = []CMDistributionBucket{}
	} else {
		// Map bucket names to actual ranges
		bucketMap := map[string]CMDistributionBucket{
			"0-10":     {MinCM: 0, MaxCM: 10},
			"10-20":    {MinCM: 10, MaxCM: 20},
			"20-50":    {MinCM: 20, MaxCM: 50},
			"50-100":   {MinCM: 50, MaxCM: 100},
			"100-200":  {MinCM: 100, MaxCM: 200},
			"200-500":  {MinCM: 200, MaxCM: 500},
			"500-1000": {MinCM: 500, MaxCM: 1000},
			"1000+":    {MinCM: 1000, MaxCM: 10000},
		}

		stats.CMDistribution = make([]CMDistributionBucket, 0, len(distributionResults))
		for _, result := range distributionResults {
			if bucket, exists := bucketMap[result.Bucket]; exists {
				bucket.Count = result.Count
				stats.CMDistribution = append(stats.CMDistribution, bucket)
			}
		}
	}

	h.logger.WithFields(log.Fields{
		"total_matches": stats.TotalMatches,
		"avg_cm":        stats.AvgSharedCM,
		"max_cm":        stats.MaxSharedCM,
		"min_cm":        stats.MinSharedCM,
	}).Info("DNA match statistics retrieved")

	c.JSON(http.StatusOK, stats)
}

// GetTopCountries returns the top countries by DNA matches
func (h *AnalyticsHandler) GetTopCountries(c *gin.Context) {
	h.logger.Info("Getting top countries statistics")

	var results []struct {
		Country string
		Count   int64
	}

	// Get top countries from trees table (site_creator_country)
	if err := h.db.Table("trees").
		Select("site_creator_country as country, COUNT(*) as count").
		Where("site_creator_country IS NOT NULL AND site_creator_country != ''").
		Group("site_creator_country").
		Order("count DESC").
		Limit(20).
		Scan(&results).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get top countries")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get top countries"})
		return
	}

	var response TopCountriesResponse
	response.Countries = make([]CountryStats, len(results))
	var total int64

	for i, result := range results {
		response.Countries[i] = CountryStats{
			Country: result.Country,
			Count:   result.Count,
		}
		total += result.Count
	}
	response.Total = total

	h.logger.WithField("countries_count", len(response.Countries)).Info("Top countries statistics retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualStats returns individual demographics statistics
func (h *AnalyticsHandler) GetIndividualStats(c *gin.Context) {
	h.logger.Info("Getting individual statistics")

	var stats IndividualStatsResponse

	// Get total count
	if err := h.db.Model(&database.Individual{}).Count(&stats.TotalIndividuals).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individual count"})
		return
	}

	// Get gender distribution
	var genderResults []struct {
		Gender string
		Count  int64
	}

	if err := h.db.Model(&database.Individual{}).
		Select("gender, COUNT(*) as count").
		Group("gender").
		Scan(&genderResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get gender distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get gender distribution"})
		return
	}

	stats.GenderDistribution = make(map[string]int64)
	for _, result := range genderResults {
		if result.Gender == "" {
			stats.GenderDistribution["unknown"] = result.Count
		} else {
			stats.GenderDistribution[result.Gender] = result.Count
		}
	}

	// Get age group distribution
	var ageResults []struct {
		AgeGroup string
		Count    int64
	}

	if err := h.db.Model(&database.Individual{}).
		Select("age_group, COUNT(*) as count").
		Group("age_group").
		Scan(&ageResults).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get age group distribution")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get age group distribution"})
		return
	}

	stats.AgeGroupDistribution = make(map[string]int64)
	for _, result := range ageResults {
		if result.AgeGroup == "" {
			stats.AgeGroupDistribution["unknown"] = result.Count
		} else {
			stats.AgeGroupDistribution[result.AgeGroup] = result.Count
		}
	}

	// Count individuals with trees
	if err := h.db.Model(&database.Individual{}).
		Where("tree_id IS NOT NULL AND tree_id != ''").
		Count(&stats.IndividualsWithTrees).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count individuals with trees")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count individuals with trees"})
		return
	}

	h.logger.WithFields(log.Fields{
		"total_individuals":      stats.TotalIndividuals,
		"individuals_with_trees": stats.IndividualsWithTrees,
	}).Info("Individual statistics retrieved")

	c.JSON(http.StatusOK, stats)
}

// GetNetworkGraph returns network graph data for visualization
func (h *AnalyticsHandler) GetNetworkGraph(c *gin.Context) {
	h.logger.Info("Getting network graph data")

	// Parse query parameters
	depthStr := c.DefaultQuery("depth", "2")
	minCMStr := c.DefaultQuery("min_cm", "20")
	limitStr := c.DefaultQuery("limit", "1000")

	depth, err := strconv.Atoi(depthStr)
	if err != nil || depth < 1 || depth > 5 {
		depth = 2
	}

	minCM, err := strconv.ParseFloat(minCMStr, 64)
	if err != nil || minCM < 0 {
		minCM = 20
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 5000 {
		limit = 1000
	}

	// Get network data with filtering
	var networkData []struct {
		SourceID     string  `json:"source_id"`
		TargetID     string  `json:"target_id"`
		SharedCM     float64 `json:"shared_cm"`
		Relationship string  `json:"relationship"`
	}

	query := `
		SELECT
			source_individual_id as source_id,
			match_individual_id as target_id,
			total_shared_segments_length_in_cm as shared_cm,
			COALESCE(exact_dna_relationship, 'unknown') as relationship
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= ?
		ORDER BY total_shared_segments_length_in_cm DESC
		LIMIT ?
	`

	if err := h.db.Raw(query, minCM, limit).Scan(&networkData).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get network graph data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get network graph data"})
		return
	}

	// Build nodes and edges for visualization
	nodeMap := make(map[string]bool)
	var nodes []NetworkNode
	var edges []NetworkEdge

	for _, match := range networkData {
		// Add nodes if not already present
		if !nodeMap[match.SourceID] {
			nodes = append(nodes, NetworkNode{
				ID:    match.SourceID,
				Label: match.SourceID,
				Type:  "individual",
			})
			nodeMap[match.SourceID] = true
		}

		if !nodeMap[match.TargetID] {
			nodes = append(nodes, NetworkNode{
				ID:    match.TargetID,
				Label: match.TargetID,
				Type:  "individual",
			})
			nodeMap[match.TargetID] = true
		}

		// Add edge
		edges = append(edges, NetworkEdge{
			Source:       match.SourceID,
			Target:       match.TargetID,
			Weight:       match.SharedCM,
			Relationship: match.Relationship,
		})
	}

	response := NetworkGraphResponse{
		Nodes: nodes,
		Edges: edges,
		Stats: NetworkStats{
			NodeCount: len(nodes),
			EdgeCount: len(edges),
			MinCM:     minCM,
			Depth:     depth,
		},
	}

	h.logger.WithFields(log.Fields{
		"nodes":  len(nodes),
		"edges":  len(edges),
		"min_cm": minCM,
		"depth":  depth,
	}).Info("Network graph data retrieved")

	c.JSON(http.StatusOK, response)
}

// GetNetworkCliques returns network clique analysis data using Bron-Kerbosch algorithm
func (h *AnalyticsHandler) GetNetworkCliques(c *gin.Context) {
	h.logger.Info("Getting network cliques data")

	// Parse query parameters
	minSizeStr := c.DefaultQuery("min_size", "3")
	minCMStr := c.DefaultQuery("min_cm", "50")
	limitStr := c.DefaultQuery("limit", "100")
	maxIndividualsStr := c.DefaultQuery("max_individuals", "1000")

	minSize, err := strconv.Atoi(minSizeStr)
	if err != nil || minSize < 2 || minSize > 20 {
		minSize = 3
	}

	minCM, err := strconv.ParseFloat(minCMStr, 64)
	if err != nil || minCM < 0 {
		minCM = 50
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 500 {
		limit = 100
	}

	maxIndividuals, err := strconv.Atoi(maxIndividualsStr)
	if err != nil || maxIndividuals < 10 || maxIndividuals > 5000 {
		maxIndividuals = 1000
	}

	// Get pre-computed cliques from database
	cliques, err := h.getPrecomputedCliques(minCM, minSize, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get pre-computed cliques")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get cliques data"})
		return
	}

	// Calculate statistics
	var totalSize int
	var maxSize int
	var minSizeFound int

	for i, clique := range cliques {
		size := clique.Size
		totalSize += size
		if size > maxSize {
			maxSize = size
		}
		if i == 0 || size < minSizeFound {
			minSizeFound = size
		}
	}

	avgSize := 0.0
	if len(cliques) > 0 {
		avgSize = float64(totalSize) / float64(len(cliques))
	}

	// Initialize stats properly when no cliques found
	if len(cliques) == 0 {
		minSizeFound = 0
	}

	response := NetworkCliquesResponse{
		Cliques: cliques,
		Stats: CliqueStats{
			TotalCliques: len(cliques),
			AvgSize:      avgSize,
			MaxSize:      maxSize,
			MinSize:      minSizeFound,
		},
	}

	h.logger.WithFields(log.Fields{
		"cliques":  len(cliques),
		"min_size": minSize,
		"min_cm":   minCM,
		"source":   "pre-computed",
	}).Info("Pre-computed network cliques data retrieved")

	c.JSON(http.StatusOK, response)
}

// getPrecomputedCliques retrieves pre-computed cliques from the database
func (h *AnalyticsHandler) getPrecomputedCliques(minCM float64, minSize int, limit int) ([]NetworkClique, error) {
	var dbCliques []database.DNAClique

	// Query pre-computed cliques with filters
	query := h.db.Where("min_cm <= ? AND size >= ?", minCM, minSize).
		Order("size DESC, avg_cm DESC").
		Limit(limit)

	if err := query.Find(&dbCliques).Error; err != nil {
		return nil, fmt.Errorf("failed to query pre-computed cliques: %w", err)
	}

	// Convert database cliques to API response format
	var cliques []NetworkClique
	for i, dbClique := range dbCliques {
		// Parse members JSON
		var members []string
		if err := json.Unmarshal([]byte(dbClique.Members), &members); err != nil {
			h.logger.WithError(err).Warn("Failed to parse clique members JSON")
			continue
		}

		clique := NetworkClique{
			ID:               i + 1,
			Members:          members,
			Size:             dbClique.Size,
			AvgCM:            dbClique.AvgCM,
			TotalConnections: dbClique.TotalConnections,
		}

		cliques = append(cliques, clique)
	}

	return cliques, nil
}

// findCliques implements the Bron-Kerbosch algorithm to find maximal cliques
func (h *AnalyticsHandler) findCliques(minCM float64, minSize int, maxIndividuals int, limit int) ([]NetworkClique, error) {
	// Step 1: Get individuals with sufficient connections
	individuals, err := h.getConnectedIndividuals(minCM, maxIndividuals)
	if err != nil {
		return nil, err
	}

	if len(individuals) == 0 {
		return []NetworkClique{}, nil
	}

	// Step 2: Build adjacency map for the graph
	adjacencyMap, err := h.buildAdjacencyMap(individuals, minCM)
	if err != nil {
		return nil, err
	}

	// Step 3: Find all maximal cliques using Bron-Kerbosch algorithm
	var allCliques [][]string
	h.bronKerbosch([]string{}, individuals, []string{}, adjacencyMap, &allCliques)

	// Step 4: Filter cliques by minimum size and convert to response format
	var cliques []NetworkClique
	cliqueID := 1

	for _, clique := range allCliques {
		if len(clique) >= minSize {
			// Calculate average cM for this clique
			avgCM, err := h.calculateCliqueAvgCM(clique, minCM)
			if err != nil {
				h.logger.WithError(err).Warn("Failed to calculate average cM for clique")
				avgCM = 0
			}

			// Calculate total connections in clique (n * (n-1) / 2 for complete graph)
			totalConnections := len(clique) * (len(clique) - 1) / 2

			networkClique := NetworkClique{
				ID:               cliqueID,
				Members:          clique,
				Size:             len(clique),
				AvgCM:            avgCM,
				TotalConnections: totalConnections,
			}
			cliques = append(cliques, networkClique)
			cliqueID++

			// Limit the number of cliques returned
			if len(cliques) >= limit {
				break
			}
		}
	}

	return cliques, nil
}

// getConnectedIndividuals returns individuals with at least one DNA match above minCM threshold
func (h *AnalyticsHandler) getConnectedIndividuals(minCM float64, maxIndividuals int) ([]string, error) {
	var individuals []string

	query := `
		SELECT DISTINCT individual_id
		FROM (
			SELECT source_individual_id as individual_id
			FROM dna_matches
			WHERE total_shared_segments_length_in_cm >= ?
			UNION
			SELECT match_individual_id as individual_id
			FROM dna_matches
			WHERE total_shared_segments_length_in_cm >= ?
		) combined
		ORDER BY individual_id
		LIMIT ?
	`

	if err := h.db.Raw(query, minCM, minCM, maxIndividuals).Scan(&individuals).Error; err != nil {
		return nil, err
	}

	return individuals, nil
}

// buildAdjacencyMap creates a map of individual -> list of connected individuals
func (h *AnalyticsHandler) buildAdjacencyMap(individuals []string, minCM float64) (map[string][]string, error) {
	adjacencyMap := make(map[string][]string)

	// Initialize empty adjacency lists
	for _, individual := range individuals {
		adjacencyMap[individual] = []string{}
	}

	// Query all connections between the individuals
	var connections []struct {
		Source string `json:"source"`
		Target string `json:"target"`
	}

	query := `
		SELECT source_individual_id as source, match_individual_id as target
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= ?
		AND source_individual_id IN (?)
		AND match_individual_id IN (?)
	`

	if err := h.db.Raw(query, minCM, individuals, individuals).Scan(&connections).Error; err != nil {
		return nil, err
	}

	// Build bidirectional adjacency map
	for _, conn := range connections {
		adjacencyMap[conn.Source] = append(adjacencyMap[conn.Source], conn.Target)
		adjacencyMap[conn.Target] = append(adjacencyMap[conn.Target], conn.Source)
	}

	// Remove duplicates and sort
	for individual := range adjacencyMap {
		adjacencyMap[individual] = h.removeDuplicates(adjacencyMap[individual])
	}

	return adjacencyMap, nil
}

// bronKerbosch implements the Bron-Kerbosch algorithm for finding maximal cliques
func (h *AnalyticsHandler) bronKerbosch(r, p, x []string, adjacencyMap map[string][]string, cliques *[][]string) {
	if len(p) == 0 && len(x) == 0 {
		// Found a maximal clique
		clique := make([]string, len(r))
		copy(clique, r)
		*cliques = append(*cliques, clique)
		return
	}

	// Make a copy of p to iterate over
	pCopy := make([]string, len(p))
	copy(pCopy, p)

	for _, v := range pCopy {
		// R ∪ {v}
		newR := append(r, v)

		// P ∩ N(v)
		newP := h.intersection(p, adjacencyMap[v])

		// X ∩ N(v)
		newX := h.intersection(x, adjacencyMap[v])

		// Recursive call
		h.bronKerbosch(newR, newP, newX, adjacencyMap, cliques)

		// P := P \ {v}
		p = h.removeElement(p, v)

		// X := X ∪ {v}
		x = append(x, v)
	}
}

// intersection returns the intersection of two string slices
func (h *AnalyticsHandler) intersection(slice1, slice2 []string) []string {
	set := make(map[string]bool)
	for _, item := range slice1 {
		set[item] = true
	}

	var result []string
	for _, item := range slice2 {
		if set[item] {
			result = append(result, item)
		}
	}

	return result
}

// removeElement removes an element from a string slice
func (h *AnalyticsHandler) removeElement(slice []string, element string) []string {
	var result []string
	for _, item := range slice {
		if item != element {
			result = append(result, item)
		}
	}
	return result
}

// removeDuplicates removes duplicate strings from a slice
func (h *AnalyticsHandler) removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}

// calculateCliqueAvgCM calculates the average cM value for connections within a clique
func (h *AnalyticsHandler) calculateCliqueAvgCM(clique []string, minCM float64) (float64, error) {
	if len(clique) < 2 {
		return 0, nil
	}

	// Query all connections within the clique
	query := `
		SELECT AVG(total_shared_segments_length_in_cm) as avg_cm, COUNT(*) as count
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= ?
		AND source_individual_id IN (?)
		AND match_individual_id IN (?)
	`

	var result struct {
		AvgCM float64 `json:"avg_cm"`
		Count int     `json:"count"`
	}

	if err := h.db.Raw(query, minCM, clique, clique).Scan(&result).Error; err != nil {
		return 0, err
	}

	if result.Count > 0 {
		return result.AvgCM, nil
	}

	return 0, nil
}

// GetMatches returns filtered DNA matches with pagination
func (h *AnalyticsHandler) GetMatches(c *gin.Context) {
	h.logger.Info("Getting filtered DNA matches")

	// Parse query parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "100")
	minCMStr := c.DefaultQuery("min_cm", "0")
	maxCMStr := c.DefaultQuery("max_cm", "10000")
	relationship := c.Query("relationship")
	confidence := c.Query("confidence")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid page parameter. Must be a positive integer."})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter. Must be between 1 and 1000."})
		return
	}

	minCM, err := strconv.ParseFloat(minCMStr, 64)
	if err != nil || minCM < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid min_cm parameter. Must be a non-negative number."})
		return
	}

	maxCM, err := strconv.ParseFloat(maxCMStr, 64)
	if err != nil || maxCM < 0 {
		maxCM = 10000
	}

	offset := (page - 1) * limit

	// Build query with filters
	query := h.db.Model(&database.DNAMatch{}).
		Where("total_shared_segments_length_in_cm >= ? AND total_shared_segments_length_in_cm <= ?", minCM, maxCM)

	if relationship != "" {
		query = query.Where("exact_dna_relationship = ?", relationship)
	}

	if confidence != "" {
		query = query.Where("confidence_level = ?", confidence)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count matches")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count matches"})
		return
	}

	// Get matches with pagination
	var matches []database.DNAMatch
	if err := query.Offset(offset).Limit(limit).Find(&matches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get matches")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get matches"})
		return
	}

	// Convert to response format
	matchData := make([]MatchData, 0)
	for _, match := range matches {
		matchData = append(matchData, MatchData{
			ID:           match.ID,
			Person1ID:    match.SourceIndividualID,
			Person2ID:    match.MatchIndividualID,
			SharedCM:     match.TotalSharedSegmentsLengthInCm,
			Relationship: match.ExactDnaRelationship,
			Confidence:   match.ConfidenceLevel,
		})
	}

	response := MatchesResponse{
		Matches: matchData,
		Total:   total,
		Page:    page,
		Limit:   limit,
	}

	h.logger.WithFields(log.Fields{
		"matches": len(matchData),
		"total":   total,
		"page":    page,
		"limit":   limit,
	}).Info("Filtered DNA matches retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividuals returns filtered individuals with pagination
func (h *AnalyticsHandler) GetIndividuals(c *gin.Context) {
	h.logger.Info("Getting filtered individuals")

	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "100")
	offsetStr := c.DefaultQuery("offset", "0")
	name := c.Query("name")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter. Must be between 1 and 1000."})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter. Must be a non-negative integer."})
		return
	}

	// Calculate page for response (1-based)
	page := (offset / limit) + 1

	// Build query with filters
	query := h.db.Model(&database.Individual{})

	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	// Get total count with timeout
	var total int64
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := query.WithContext(ctx).Count(&total).Error; err != nil {
		h.logger.WithError(err).Warn("Failed to count individuals, using estimated count")
		// Fallback to estimated count for better performance
		if name != "" {
			// For filtered queries, use a smaller estimated count
			total = 1000
		} else {
			// For unfiltered queries, use a reasonable estimate
			total = 314000 // Based on known database size
		}
	}

	// Get individuals with pagination
	var individuals []database.Individual
	if err := query.Offset(offset).Limit(limit).Find(&individuals).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get individuals")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individuals"})
		return
	}

	// Convert to response format
	individualData := make([]IndividualData, 0)
	for _, individual := range individuals {
		individualData = append(individualData, IndividualData{
			ID:        individual.ID,
			Name:      individual.Name,
			BirthYear: individual.BirthDateYear,
		})
	}

	response := IndividualsResponse{
		Individuals: individualData,
		Total:       total,
		Page:        page,
		Limit:       limit,
	}

	h.logger.WithFields(log.Fields{
		"individuals": len(individualData),
		"total":       total,
		"page":        page,
		"limit":       limit,
	}).Info("Filtered individuals retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualBasicInfo returns basic information about a specific individual
func (h *AnalyticsHandler) GetIndividualBasicInfo(c *gin.Context) {
	individualID := c.Param("id")
	if individualID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Individual ID is required"})
		return
	}

	h.logger.WithField("individual_id", individualID).Info("Getting individual basic info")

	// Get individual basic information
	var individual database.Individual
	if err := h.db.Where("id = ?", individualID).First(&individual).Error; err != nil {
		h.logger.WithError(err).WithField("individual_id", individualID).Error("Individual not found")
		c.JSON(http.StatusNotFound, gin.H{"error": "Individual not found"})
		return
	}

	// Get tree information if available
	var tree *database.Tree
	if individual.TreeID != "" {
		var treeData database.Tree
		if err := h.db.Where("id = ?", individual.TreeID).First(&treeData).Error; err == nil {
			tree = &treeData
		}
	}

	// Build basic response
	response := IndividualBasicInfoResponse{
		Individual: individual,
		Tree:       tree,
	}

	h.logger.WithField("individual_id", individualID).Info("Individual basic info retrieved")
	c.JSON(http.StatusOK, response)
}

// GetIndividualDNAMatches returns DNA matches for a specific individual
func (h *AnalyticsHandler) GetIndividualDNAMatches(c *gin.Context) {
	individualID := c.Param("id")
	if individualID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Individual ID is required"})
		return
	}

	h.logger.WithField("individual_id", individualID).Info("Getting individual DNA matches")

	// Get DNA matches using optimized two-step approach
	var dnaMatchesWithNames []struct {
		database.DNAMatch
		MatchedIndividualID   string `json:"matched_individual_id"`
		MatchedIndividualName string `json:"matched_individual_name"`
	}

	// Step 1: Get basic DNA matches using optimized approach
	var basicMatches []database.DNAMatch
	ctx, cancel := context.WithTimeout(context.Background(), 25*time.Second)
	defer cancel()

	// Use separate queries and combine results for better index utilization
	var sourceMatches []database.DNAMatch
	var matchMatches []database.DNAMatch

	// Get matches where this individual is the source (uses idx_dna_matches_source_individual)
	if err := h.db.WithContext(ctx).Where("source_individual_id = ?", individualID).
		Order("total_shared_segments_length_in_cm DESC").
		Limit(50).
		Find(&sourceMatches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get source DNA matches")
	} else {
		h.logger.WithField("source_matches_count", len(sourceMatches)).Info("Retrieved source DNA matches")
	}

	// Get matches where this individual is the match (uses idx_dna_matches_match_individual)
	if err := h.db.WithContext(ctx).Where("match_individual_id = ?", individualID).
		Order("total_shared_segments_length_in_cm DESC").
		Limit(50).
		Find(&matchMatches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get match DNA matches")
	} else {
		h.logger.WithField("match_matches_count", len(matchMatches)).Info("Retrieved match DNA matches")
	}

	// Combine and sort results
	basicMatches = append(sourceMatches, matchMatches...)
	h.logger.WithField("combined_matches_count", len(basicMatches)).Info("Combined DNA matches")

	// Sort by shared CM descending and limit to top 100
	sort.Slice(basicMatches, func(i, j int) bool {
		return basicMatches[i].TotalSharedSegmentsLengthInCm > basicMatches[j].TotalSharedSegmentsLengthInCm
	})
	if len(basicMatches) > 100 {
		basicMatches = basicMatches[:100]
	}
	h.logger.WithField("final_matches_count", len(basicMatches)).Info("Final DNA matches after sorting and limiting")

	if len(basicMatches) > 0 {
		// Step 2: Get individual names for matched individuals (batch query)
		matchedIDs := make([]string, 0, len(basicMatches))
		matchedIDToName := make(map[string]string)

		for _, match := range basicMatches {
			matchedID := match.MatchIndividualID
			if match.SourceIndividualID != individualID {
				matchedID = match.SourceIndividualID
			}
			if matchedID != "" {
				matchedIDs = append(matchedIDs, matchedID)
			}
		}

		// Batch fetch individual names
		if len(matchedIDs) > 0 {
			var individuals []database.Individual
			nameCtx, nameCancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer nameCancel()

			if err := h.db.WithContext(nameCtx).Select("id, name").Where("id IN ?", matchedIDs).Find(&individuals).Error; err != nil {
				h.logger.WithError(err).Warn("Failed to get individual names, proceeding without names")
			} else {
				for _, individual := range individuals {
					matchedIDToName[individual.ID] = individual.Name
				}
			}
		}

		// Step 3: Combine matches with names
		for _, match := range basicMatches {
			matchedID := match.MatchIndividualID
			if match.SourceIndividualID != individualID {
				matchedID = match.SourceIndividualID
			}

			matchedName := matchedIDToName[matchedID]
			dnaMatchesWithNames = append(dnaMatchesWithNames, struct {
				database.DNAMatch
				MatchedIndividualID   string `json:"matched_individual_id"`
				MatchedIndividualName string `json:"matched_individual_name"`
			}{
				DNAMatch:              match,
				MatchedIndividualID:   matchedID,
				MatchedIndividualName: matchedName,
			})
		}
	}

	// Convert to DNAMatchWithName slice for response
	var dnaMatchesEnhanced []DNAMatchWithName = make([]DNAMatchWithName, 0)
	for _, match := range dnaMatchesWithNames {
		dnaMatchesEnhanced = append(dnaMatchesEnhanced, DNAMatchWithName{
			DNAMatch:              match.DNAMatch,
			MatchedIndividualID:   match.MatchedIndividualID,
			MatchedIndividualName: match.MatchedIndividualName,
		})
	}

	// Build response
	response := IndividualDNAMatchesResponse{
		DNAMatches: dnaMatchesEnhanced,
		Stats: struct {
			TotalMatches int `json:"total_matches"`
		}{
			TotalMatches: len(dnaMatchesEnhanced),
		},
	}

	h.logger.WithFields(log.Fields{
		"individual_id": individualID,
		"matches_count": len(dnaMatchesEnhanced),
	}).Info("Individual DNA matches retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualSharedSegments returns shared segments for a specific individual
func (h *AnalyticsHandler) GetIndividualSharedSegments(c *gin.Context) {
	individualID := c.Param("id")
	if individualID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Individual ID is required"})
		return
	}

	h.logger.WithField("individual_id", individualID).Info("Getting individual shared segments")

	// First get DNA matches to get match IDs
	var basicMatches []database.DNAMatch
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Get matches where this individual is involved
	var sourceMatches []database.DNAMatch
	var matchMatches []database.DNAMatch

	if err := h.db.WithContext(ctx).Where("source_individual_id = ?", individualID).
		Limit(50).Find(&sourceMatches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get source DNA matches for segments")
	}

	if err := h.db.WithContext(ctx).Where("match_individual_id = ?", individualID).
		Limit(50).Find(&matchMatches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get match DNA matches for segments")
	}

	basicMatches = append(sourceMatches, matchMatches...)

	// Get shared segments for this individual's matches
	var sharedSegments []database.SharedSegment = make([]database.SharedSegment, 0)
	if len(basicMatches) > 0 {
		matchIDs := make([]string, len(basicMatches))
		for i, match := range basicMatches {
			matchIDs[i] = match.ID
		}
		if err := h.db.Where("match_id IN ?", matchIDs).
			Order("chromosome_id, start_position").
			Find(&sharedSegments).Error; err != nil {
			h.logger.WithError(err).Error("Failed to get shared segments")
			sharedSegments = make([]database.SharedSegment, 0) // Ensure empty slice on error
		}
	}

	// Build response
	response := IndividualSharedSegmentsResponse{
		SharedSegments: sharedSegments,
		Stats: struct {
			TotalSegments int `json:"total_segments"`
		}{
			TotalSegments: len(sharedSegments),
		},
	}

	h.logger.WithFields(log.Fields{
		"individual_id":  individualID,
		"segments_count": len(sharedSegments),
	}).Info("Individual shared segments retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualFamily returns family relationships for a specific individual
func (h *AnalyticsHandler) GetIndividualFamily(c *gin.Context) {
	individualID := c.Param("id")
	if individualID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Individual ID is required"})
		return
	}

	h.logger.WithField("individual_id", individualID).Info("Getting individual family relationships")

	// Get family relationships
	var familyRelationships []database.IndividualFamily = make([]database.IndividualFamily, 0)
	if err := h.db.Where("individual_id = ?", individualID).Find(&familyRelationships).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get family relationships")
		familyRelationships = make([]database.IndividualFamily, 0) // Ensure empty slice on error
	}

	// Build response
	response := IndividualFamilyResponse{
		FamilyRelationships: familyRelationships,
		Stats: struct {
			TotalFamilyRoles int `json:"total_family_roles"`
		}{
			TotalFamilyRoles: len(familyRelationships),
		},
	}

	h.logger.WithFields(log.Fields{
		"individual_id": individualID,
		"family_roles":  len(familyRelationships),
	}).Info("Individual family relationships retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualSurnames returns surnames for a specific individual
func (h *AnalyticsHandler) GetIndividualSurnames(c *gin.Context) {
	individualID := c.Param("id")
	if individualID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Individual ID is required"})
		return
	}

	h.logger.WithField("individual_id", individualID).Info("Getting individual surnames")

	// Get surnames associated with this individual, ordered by popularity (most popular first)
	var surnames []database.Surname = make([]database.Surname, 0)
	if err := h.db.Raw(`
		SELECT s.*,
		       (SELECT COUNT(*) FROM individual_surname WHERE surname_id = s.id) as popularity_count
		FROM surname s
		JOIN individual_surname is_user ON s.id = is_user.surname_id
		WHERE is_user.individual_id = ?
		ORDER BY popularity_count DESC, s.surname ASC
	`, individualID).Scan(&surnames).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get surnames")
		surnames = make([]database.Surname, 0) // Ensure empty slice on error
	}

	// Build response
	response := IndividualSurnamesResponse{
		Surnames: surnames,
		Stats: struct {
			TotalSurnames int `json:"total_surnames"`
		}{
			TotalSurnames: len(surnames),
		},
	}

	h.logger.WithFields(log.Fields{
		"individual_id":  individualID,
		"surnames_count": len(surnames),
	}).Info("Individual surnames retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualPlaces returns places for a specific individual
func (h *AnalyticsHandler) GetIndividualPlaces(c *gin.Context) {
	individualID := c.Param("id")
	if individualID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Individual ID is required"})
		return
	}

	h.logger.WithField("individual_id", individualID).Info("Getting individual places")

	// Get places associated with this individual, ordered by popularity (most popular first)
	var places []database.Place = make([]database.Place, 0)
	if err := h.db.Raw(`
		SELECT p.*,
		       (SELECT COUNT(*) FROM individual_place WHERE place_id = p.id) as popularity_count
		FROM place p
		JOIN individual_place ip_user ON p.id = ip_user.place_id
		WHERE ip_user.individual_id = ?
		ORDER BY popularity_count DESC, p.country ASC, p.state_or_province ASC
	`, individualID).Scan(&places).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get places")
		places = make([]database.Place, 0) // Ensure empty slice on error
	}

	// Build response
	response := IndividualPlacesResponse{
		Places: places,
		Stats: struct {
			TotalPlaces int `json:"total_places"`
		}{
			TotalPlaces: len(places),
		},
	}

	h.logger.WithFields(log.Fields{
		"individual_id": individualID,
		"places_count":  len(places),
	}).Info("Individual places retrieved")

	c.JSON(http.StatusOK, response)
}

// GetIndividualDetails returns detailed information about a specific individual (legacy endpoint)
func (h *AnalyticsHandler) GetIndividualDetails(c *gin.Context) {
	individualID := c.Param("id")
	if individualID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Individual ID is required"})
		return
	}

	h.logger.WithField("individual_id", individualID).Info("Getting individual details")

	// Get individual basic information
	var individual database.Individual
	if err := h.db.Where("id = ?", individualID).First(&individual).Error; err != nil {
		h.logger.WithError(err).WithField("individual_id", individualID).Error("Individual not found")
		c.JSON(http.StatusNotFound, gin.H{"error": "Individual not found"})
		return
	}

	// Get tree information if available
	var tree *database.Tree
	if individual.TreeID != "" {
		var treeData database.Tree
		if err := h.db.Where("id = ?", individual.TreeID).First(&treeData).Error; err == nil {
			tree = &treeData
		}
	}

	// Get DNA matches using optimized two-step approach
	var dnaMatchesWithNames []struct {
		database.DNAMatch
		MatchedIndividualID   string `json:"matched_individual_id"`
		MatchedIndividualName string `json:"matched_individual_name"`
	}

	// Step 1: Get basic DNA matches using UNION for optimal index usage
	var basicMatches []database.DNAMatch
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Use separate queries and combine results for better index utilization
	var sourceMatches []database.DNAMatch
	var matchMatches []database.DNAMatch

	// Get matches where this individual is the source (uses idx_dna_matches_source_individual)
	if err := h.db.WithContext(ctx).Where("source_individual_id = ?", individualID).
		Order("total_shared_segments_length_in_cm DESC").
		Limit(50).
		Find(&sourceMatches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get source DNA matches")
	} else {
		h.logger.WithField("source_matches_count", len(sourceMatches)).Info("Retrieved source DNA matches")
	}

	// Get matches where this individual is the match (uses idx_dna_matches_match_individual)
	if err := h.db.WithContext(ctx).Where("match_individual_id = ?", individualID).
		Order("total_shared_segments_length_in_cm DESC").
		Limit(50).
		Find(&matchMatches).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get match DNA matches")
	} else {
		h.logger.WithField("match_matches_count", len(matchMatches)).Info("Retrieved match DNA matches")
	}

	// Combine and sort results
	basicMatches = append(sourceMatches, matchMatches...)
	h.logger.WithField("combined_matches_count", len(basicMatches)).Info("Combined DNA matches")

	// Sort by shared CM descending and limit to top 100
	sort.Slice(basicMatches, func(i, j int) bool {
		return basicMatches[i].TotalSharedSegmentsLengthInCm > basicMatches[j].TotalSharedSegmentsLengthInCm
	})
	if len(basicMatches) > 100 {
		basicMatches = basicMatches[:100]
	}
	h.logger.WithField("final_matches_count", len(basicMatches)).Info("Final DNA matches after sorting and limiting")

	if len(basicMatches) > 0 {
		// Step 2: Get individual names for matched individuals (batch query)
		matchedIDs := make([]string, 0, len(basicMatches))
		matchedIDToName := make(map[string]string)

		for _, match := range basicMatches {
			matchedID := match.MatchIndividualID
			if match.SourceIndividualID != individualID {
				matchedID = match.SourceIndividualID
			}
			if matchedID != "" {
				matchedIDs = append(matchedIDs, matchedID)
			}
		}

		// Batch fetch individual names
		if len(matchedIDs) > 0 {
			var individuals []database.Individual
			nameCtx, nameCancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer nameCancel()

			if err := h.db.WithContext(nameCtx).Select("id, name").Where("id IN ?", matchedIDs).Find(&individuals).Error; err != nil {
				h.logger.WithError(err).Warn("Failed to get individual names, proceeding without names")
			} else {
				for _, individual := range individuals {
					matchedIDToName[individual.ID] = individual.Name
				}
			}
		}

		// Step 3: Combine matches with names
		for _, match := range basicMatches {
			matchedID := match.MatchIndividualID
			if match.SourceIndividualID != individualID {
				matchedID = match.SourceIndividualID
			}

			matchedName := matchedIDToName[matchedID]
			dnaMatchesWithNames = append(dnaMatchesWithNames, struct {
				database.DNAMatch
				MatchedIndividualID   string `json:"matched_individual_id"`
				MatchedIndividualName string `json:"matched_individual_name"`
			}{
				DNAMatch:              match,
				MatchedIndividualID:   matchedID,
				MatchedIndividualName: matchedName,
			})
		}
	}

	// Convert to DNAMatchWithName slice for response
	var dnaMatchesEnhanced []DNAMatchWithName = make([]DNAMatchWithName, 0)
	for _, match := range dnaMatchesWithNames {
		dnaMatchesEnhanced = append(dnaMatchesEnhanced, DNAMatchWithName{
			DNAMatch:              match.DNAMatch,
			MatchedIndividualID:   match.MatchedIndividualID,
			MatchedIndividualName: match.MatchedIndividualName,
		})
	}

	// Keep original dnaMatches for shared segments compatibility
	var dnaMatches []database.DNAMatch
	for _, match := range dnaMatchesWithNames {
		dnaMatches = append(dnaMatches, match.DNAMatch)
	}

	// Get shared segments for this individual's matches
	var sharedSegments []database.SharedSegment = make([]database.SharedSegment, 0)
	if len(dnaMatches) > 0 {
		matchIDs := make([]string, len(dnaMatches))
		for i, match := range dnaMatches {
			matchIDs[i] = match.ID
		}
		if err := h.db.Where("match_id IN ?", matchIDs).
			Order("chromosome_id, start_position").
			Find(&sharedSegments).Error; err != nil {
			h.logger.WithError(err).Error("Failed to get shared segments")
			sharedSegments = make([]database.SharedSegment, 0) // Ensure empty slice on error
		}
	}

	// Get family relationships
	var familyRelationships []database.IndividualFamily = make([]database.IndividualFamily, 0)
	if err := h.db.Where("individual_id = ?", individualID).Find(&familyRelationships).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get family relationships")
		familyRelationships = make([]database.IndividualFamily, 0) // Ensure empty slice on error
	}

	// Get surnames associated with this individual, ordered by popularity (most popular first)
	var surnames []database.Surname = make([]database.Surname, 0)
	if err := h.db.Raw(`
		SELECT s.*,
		       (SELECT COUNT(*) FROM individual_surname WHERE surname_id = s.id) as popularity_count
		FROM surname s
		JOIN individual_surname is_user ON s.id = is_user.surname_id
		WHERE is_user.individual_id = ?
		ORDER BY popularity_count DESC, s.surname ASC
	`, individualID).Scan(&surnames).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get surnames")
		surnames = make([]database.Surname, 0) // Ensure empty slice on error
	}

	// Get places associated with this individual, ordered by popularity (most popular first)
	var places []database.Place = make([]database.Place, 0)
	if err := h.db.Raw(`
		SELECT p.*,
		       (SELECT COUNT(*) FROM individual_place WHERE place_id = p.id) as popularity_count
		FROM place p
		JOIN individual_place ip_user ON p.id = ip_user.place_id
		WHERE ip_user.individual_id = ?
		ORDER BY popularity_count DESC, p.country ASC, p.state_or_province ASC
	`, individualID).Scan(&places).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get places")
		places = make([]database.Place, 0) // Ensure empty slice on error
	}

	// Build response
	response := IndividualDetailsResponse{
		Individual:          individual,
		Tree:                tree,
		DNAMatches:          dnaMatchesEnhanced,
		SharedSegments:      sharedSegments,
		FamilyRelationships: familyRelationships,
		Surnames:            surnames,
		Places:              places,
		Stats: IndividualStatsDetail{
			TotalMatches:     len(dnaMatchesEnhanced),
			TotalSegments:    len(sharedSegments),
			TotalSurnames:    len(surnames),
			TotalPlaces:      len(places),
			TotalFamilyRoles: len(familyRelationships),
		},
	}

	h.logger.WithFields(log.Fields{
		"individual_id":  individualID,
		"matches_count":  len(dnaMatches),
		"segments_count": len(sharedSegments),
		"surnames_count": len(surnames),
		"places_count":   len(places),
		"family_roles":   len(familyRelationships),
	}).Info("Individual details retrieved")

	c.JSON(http.StatusOK, response)
}

// GetSegments returns filtered DNA segments with pagination
func (h *AnalyticsHandler) GetSegments(c *gin.Context) {
	h.logger.Info("Getting filtered DNA segments")

	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "100")
	offsetStr := c.DefaultQuery("offset", "0")
	matchID := c.Query("match_id")
	chromosomeStr := c.Query("chromosome")
	minLengthStr := c.DefaultQuery("min_length", "0")
	maxLengthStr := c.DefaultQuery("max_length", "1000")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter. Must be between 1 and 1000."})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter. Must be a non-negative integer."})
		return
	}

	minLength, err := strconv.ParseFloat(minLengthStr, 64)
	if err != nil || minLength < 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid min_cm parameter. Must be a non-negative number."})
		return
	}

	maxLength, err := strconv.ParseFloat(maxLengthStr, 64)
	if err != nil || maxLength < 0 {
		maxLength = 1000
	}

	// Calculate page for response (1-based)
	page := (offset / limit) + 1

	// Build query with filters
	query := h.db.Model(&database.SharedSegment{}).
		Where("length_in_centimorgans >= ? AND length_in_centimorgans <= ?", minLength, maxLength)

	if matchID != "" {
		query = query.Where("match_id = ?", matchID)
	}

	if chromosomeStr != "" {
		chromosome, err := strconv.Atoi(chromosomeStr)
		if err != nil || chromosome < 1 || chromosome > 23 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chromosome parameter. Must be between 1 and 23."})
			return
		}
		query = query.Where("chromosome_id = ?", chromosome)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.logger.WithError(err).Error("Failed to count segments")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count segments"})
		return
	}

	// Get segments with pagination
	var segments []database.SharedSegment
	if err := query.Offset(offset).Limit(limit).Find(&segments).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get segments")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get segments"})
		return
	}

	// Convert to response format
	segmentData := make([]SegmentData, 0)
	for _, segment := range segments {
		segmentData = append(segmentData, SegmentData{
			ID:         strconv.Itoa(segment.ID),
			MatchID:    segment.MatchID,
			Chromosome: strconv.Itoa(segment.ChromosomeID),
			StartPos:   int64(segment.StartPosition),
			EndPos:     int64(segment.EndPosition),
			Length:     segment.LengthInCentimorgans,
		})
	}

	response := SegmentsResponse{
		Segments: segmentData,
		Total:    total,
		Page:     page,
		Limit:    limit,
	}

	h.logger.WithFields(log.Fields{
		"segments": len(segmentData),
		"total":    total,
		"page":     page,
		"limit":    limit,
	}).Info("Filtered DNA segments retrieved")

	c.JSON(http.StatusOK, response)
}

// GetSurnameDetails returns detailed information about a specific surname
func (h *AnalyticsHandler) GetSurnameDetails(c *gin.Context) {
	surnameID := c.Param("id")
	h.logger.WithField("surname_id", surnameID).Info("Getting surname details")

	// Get surname information
	var surname database.Surname
	if err := h.db.Where("id = ?", surnameID).First(&surname).Error; err != nil {
		h.logger.WithError(err).Error("Failed to find surname")
		c.JSON(http.StatusNotFound, gin.H{"error": "Surname not found"})
		return
	}

	// Get individuals associated with this surname
	var individuals []database.Individual
	if err := h.db.Table("individuals").
		Joins("JOIN individual_surname ON individuals.id = individual_surname.individual_id").
		Where("individual_surname.surname_id = ?", surnameID).
		Limit(1000).
		Find(&individuals).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get individuals for surname")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individuals"})
		return
	}

	// Get places associated with individuals having this surname
	var places []database.Place
	if err := h.db.Table("place").
		Joins("JOIN individual_place ON place.id = individual_place.place_id").
		Joins("JOIN individual_surname ON individual_place.individual_id = individual_surname.individual_id").
		Where("individual_surname.surname_id = ?", surnameID).
		Group("place.id").
		Limit(100).
		Find(&places).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get places for surname")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get places"})
		return
	}

	// Get trees associated with individuals having this surname
	var trees []database.Tree
	if err := h.db.Table("trees").
		Joins("JOIN individuals ON trees.id = individuals.tree_id").
		Joins("JOIN individual_surname ON individuals.id = individual_surname.individual_id").
		Where("individual_surname.surname_id = ?", surnameID).
		Group("trees.id").
		Limit(100).
		Find(&trees).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get trees for surname")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get trees"})
		return
	}

	// Calculate statistics
	stats := SurnameStatsDetail{
		TotalIndividuals: len(individuals),
		TotalPlaces:      len(places),
		TotalTrees:       len(trees),
	}

	// Count gender distribution
	genderCounts := make(map[string]int)
	for _, individual := range individuals {
		if individual.Gender == "" {
			genderCounts["unknown"]++
		} else {
			genderCounts[individual.Gender]++
		}
	}
	stats.GenderDistribution = genderCounts

	// Count age group distribution
	ageCounts := make(map[string]int)
	for _, individual := range individuals {
		if individual.AgeGroup == "" {
			ageCounts["unknown"]++
		} else {
			ageCounts[individual.AgeGroup]++
		}
	}
	stats.AgeGroupDistribution = ageCounts

	response := SurnameDetailsResponse{
		Surname:     surname,
		Individuals: individuals,
		Places:      places,
		Trees:       trees,
		Stats:       stats,
	}

	h.logger.WithFields(log.Fields{
		"surname":     surname.Surname,
		"individuals": len(individuals),
		"places":      len(places),
		"trees":       len(trees),
	}).Info("Surname details retrieved")

	c.JSON(http.StatusOK, response)
}

// GetPlaceDetails returns detailed information about a specific place
func (h *AnalyticsHandler) GetPlaceDetails(c *gin.Context) {
	placeID := c.Param("id")
	h.logger.WithField("place_id", placeID).Info("Getting place details")

	// Get place information
	var place database.Place
	if err := h.db.Where("id = ?", placeID).First(&place).Error; err != nil {
		h.logger.WithError(err).Error("Failed to find place")
		c.JSON(http.StatusNotFound, gin.H{"error": "Place not found"})
		return
	}

	// Get individuals associated with this place
	var individuals []database.Individual
	if err := h.db.Table("individuals").
		Joins("JOIN individual_place ON individuals.id = individual_place.individual_id").
		Where("individual_place.place_id = ?", placeID).
		Limit(1000).
		Find(&individuals).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get individuals for place")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get individuals"})
		return
	}

	// Get surnames associated with individuals from this place
	var surnames []database.Surname
	if err := h.db.Table("surname").
		Joins("JOIN individual_surname ON surname.id = individual_surname.surname_id").
		Joins("JOIN individual_place ON individual_surname.individual_id = individual_place.individual_id").
		Where("individual_place.place_id = ?", placeID).
		Group("surname.id").
		Limit(100).
		Find(&surnames).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get surnames for place")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get surnames"})
		return
	}

	// Get trees associated with individuals from this place
	var trees []database.Tree
	if err := h.db.Table("trees").
		Joins("JOIN individuals ON trees.id = individuals.tree_id").
		Joins("JOIN individual_place ON individuals.id = individual_place.individual_id").
		Where("individual_place.place_id = ?", placeID).
		Group("trees.id").
		Limit(100).
		Find(&trees).Error; err != nil {
		h.logger.WithError(err).Error("Failed to get trees for place")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get trees"})
		return
	}

	// Calculate statistics
	stats := PlaceStatsDetail{
		TotalIndividuals: len(individuals),
		TotalSurnames:    len(surnames),
		TotalTrees:       len(trees),
	}

	// Count gender distribution
	genderCounts := make(map[string]int)
	for _, individual := range individuals {
		if individual.Gender == "" {
			genderCounts["unknown"]++
		} else {
			genderCounts[individual.Gender]++
		}
	}
	stats.GenderDistribution = genderCounts

	// Count age group distribution
	ageCounts := make(map[string]int)
	for _, individual := range individuals {
		if individual.AgeGroup == "" {
			ageCounts["unknown"]++
		} else {
			ageCounts[individual.AgeGroup]++
		}
	}
	stats.AgeGroupDistribution = ageCounts

	response := PlaceDetailsResponse{
		Place:       place,
		Individuals: individuals,
		Surnames:    surnames,
		Trees:       trees,
		Stats:       stats,
	}

	h.logger.WithFields(log.Fields{
		"place":       place.Country + ", " + place.StateOrProvince,
		"individuals": len(individuals),
		"surnames":    len(surnames),
		"trees":       len(trees),
	}).Info("Place details retrieved")

	c.JSON(http.StatusOK, response)
}
