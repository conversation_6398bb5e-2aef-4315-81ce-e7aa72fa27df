package api

import (
	"time"

	"github.com/dmagur/myheritage/internal/database"
)

// OverviewResponse represents the database overview statistics
type OverviewResponse struct {
	TotalDNAMatches     int64     `json:"total_dna_matches"`
	TotalIndividuals    int64     `json:"total_individuals"`
	TotalTrees          int64     `json:"total_trees"`
	TotalSharedSegments int64     `json:"total_shared_segments"`
	TotalSubmitters     int64     `json:"total_submitters"`
	TotalFamilies       int64     `json:"total_families"`
	TotalSurnames       int64     `json:"total_surnames"`
	LastUpdated         time.Time `json:"last_updated"`
}

// MatchStatsResponse represents DNA match statistics
type MatchStatsResponse struct {
	TotalMatches          int64                  `json:"total_matches"`
	AvgSharedCM           float64                `json:"avg_shared_cm"`
	MaxSharedCM           float64                `json:"max_shared_cm"`
	MinSharedCM           float64                `json:"min_shared_cm"`
	MatchesByConfidence   map[string]int64       `json:"matches_by_confidence"`
	MatchesByRelationship map[string]int64       `json:"matches_by_relationship"`
	CMDistribution        []CMDistributionBucket `json:"cm_distribution"`
}

// CMDistributionBucket represents a bucket in the cM distribution
type CMDistributionBucket struct {
	MinCM float64 `json:"min_cm"`
	MaxCM float64 `json:"max_cm"`
	Count int64   `json:"count"`
}

// TopCountriesResponse represents the top countries by DNA matches
type TopCountriesResponse struct {
	Countries []CountryStats `json:"countries"`
	Total     int64          `json:"total"`
}

// CountryStats represents statistics for a specific country
type CountryStats struct {
	Country string `json:"country"`
	Count   int64  `json:"count"`
}

// IndividualStatsResponse represents individual demographics
type IndividualStatsResponse struct {
	TotalIndividuals     int64            `json:"total_individuals"`
	GenderDistribution   map[string]int64 `json:"gender_distribution"`
	AgeGroupDistribution map[string]int64 `json:"age_group_distribution"`
	TopSurnames          []SurnameStats   `json:"top_surnames"`
	IndividualsWithTrees int64            `json:"individuals_with_trees"`
}

// SurnameStats represents statistics for a surname
type SurnameStats struct {
	Surname string `json:"surname"`
	Count   int64  `json:"count"`
}

// SurnameDetailsResponse represents detailed information about a surname
type SurnameDetailsResponse struct {
	Surname     database.Surname      `json:"surname"`
	Individuals []database.Individual `json:"individuals"`
	Places      []database.Place      `json:"places"`
	Trees       []database.Tree       `json:"trees"`
	Stats       SurnameStatsDetail    `json:"stats"`
}

// SurnameStatsDetail represents detailed statistics for a surname
type SurnameStatsDetail struct {
	TotalIndividuals     int            `json:"total_individuals"`
	TotalPlaces          int            `json:"total_places"`
	TotalTrees           int            `json:"total_trees"`
	GenderDistribution   map[string]int `json:"gender_distribution"`
	AgeGroupDistribution map[string]int `json:"age_group_distribution"`
}

// PlaceDetailsResponse represents detailed information about a place
type PlaceDetailsResponse struct {
	Place       database.Place        `json:"place"`
	Individuals []database.Individual `json:"individuals"`
	Surnames    []database.Surname    `json:"surnames"`
	Trees       []database.Tree       `json:"trees"`
	Stats       PlaceStatsDetail      `json:"stats"`
}

// PlaceStatsDetail represents detailed statistics for a place
type PlaceStatsDetail struct {
	TotalIndividuals     int            `json:"total_individuals"`
	TotalSurnames        int            `json:"total_surnames"`
	TotalTrees           int            `json:"total_trees"`
	GenderDistribution   map[string]int `json:"gender_distribution"`
	AgeGroupDistribution map[string]int `json:"age_group_distribution"`
}

// NetworkGraphResponse represents network graph data for visualization
type NetworkGraphResponse struct {
	Nodes []NetworkNode `json:"nodes"`
	Edges []NetworkEdge `json:"edges"`
	Stats NetworkStats  `json:"stats"`
}

// NetworkNode represents a node in the network graph
type NetworkNode struct {
	ID    string `json:"id"`
	Label string `json:"label"`
	Type  string `json:"type"`
}

// NetworkEdge represents an edge in the network graph
type NetworkEdge struct {
	Source       string  `json:"source"`
	Target       string  `json:"target"`
	Weight       float64 `json:"weight"`
	Relationship string  `json:"relationship"`
}

// NetworkStats represents statistics about the network graph
type NetworkStats struct {
	NodeCount int     `json:"node_count"`
	EdgeCount int     `json:"edge_count"`
	MinCM     float64 `json:"min_cm"`
	Depth     int     `json:"depth"`
}

// NetworkCliquesResponse represents clique analysis data
type NetworkCliquesResponse struct {
	Cliques []NetworkClique `json:"cliques"`
	Stats   CliqueStats     `json:"stats"`
}

// NetworkClique represents a clique in the network
type NetworkClique struct {
	ID               int      `json:"id"`
	Members          []string `json:"members"`
	Size             int      `json:"size"`
	AvgCM            float64  `json:"avg_cm"`
	TotalConnections int      `json:"total_connections"`
}

// CliqueStats represents statistics about cliques
type CliqueStats struct {
	TotalCliques int     `json:"total_cliques"`
	AvgSize      float64 `json:"avg_size"`
	MaxSize      int     `json:"max_size"`
	MinSize      int     `json:"min_size"`
}

// MatchesResponse represents filtered DNA matches
type MatchesResponse struct {
	Matches []MatchData `json:"matches"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Limit   int         `json:"limit"`
}

// MatchData represents a DNA match record
type MatchData struct {
	ID           string  `json:"id"`
	Person1ID    string  `json:"person_1_id"`
	Person2ID    string  `json:"person_2_id"`
	SharedCM     float64 `json:"shared_cm"`
	Relationship string  `json:"relationship"`
	Confidence   string  `json:"confidence"`
}

// IndividualsResponse represents filtered individuals
type IndividualsResponse struct {
	Individuals []IndividualData `json:"individuals"`
	Total       int64            `json:"total"`
	Page        int              `json:"page"`
	Limit       int              `json:"limit"`
}

// IndividualData represents an individual record
type IndividualData struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	BirthYear string `json:"birth_year"`
}

// DNAMatchWithName represents a DNA match with matched individual information
type DNAMatchWithName struct {
	database.DNAMatch
	MatchedIndividualID   string `json:"matched_individual_id"`
	MatchedIndividualName string `json:"matched_individual_name"`
}

// IndividualBasicInfoResponse represents basic information about an individual
type IndividualBasicInfoResponse struct {
	Individual database.Individual `json:"individual"`
	Tree       *database.Tree      `json:"tree,omitempty"`
}

// IndividualDNAMatchesResponse represents DNA matches for an individual
type IndividualDNAMatchesResponse struct {
	DNAMatches []DNAMatchWithName `json:"dna_matches"`
	Stats      struct {
		TotalMatches int `json:"total_matches"`
	} `json:"stats"`
}

// IndividualSharedSegmentsResponse represents shared segments for an individual
type IndividualSharedSegmentsResponse struct {
	SharedSegments []database.SharedSegment `json:"shared_segments"`
	Stats          struct {
		TotalSegments int `json:"total_segments"`
	} `json:"stats"`
}

// IndividualFamilyResponse represents family relationships for an individual
type IndividualFamilyResponse struct {
	FamilyRelationships []database.IndividualFamily `json:"family_relationships"`
	Stats               struct {
		TotalFamilyRoles int `json:"total_family_roles"`
	} `json:"stats"`
}

// IndividualSurnamesResponse represents surnames for an individual
type IndividualSurnamesResponse struct {
	Surnames []database.Surname `json:"surnames"`
	Stats    struct {
		TotalSurnames int `json:"total_surnames"`
	} `json:"stats"`
}

// IndividualPlacesResponse represents places for an individual
type IndividualPlacesResponse struct {
	Places []database.Place `json:"places"`
	Stats  struct {
		TotalPlaces int `json:"total_places"`
	} `json:"stats"`
}

// IndividualDetailsResponse represents detailed information about an individual (legacy)
type IndividualDetailsResponse struct {
	Individual          database.Individual         `json:"individual"`
	Tree                *database.Tree              `json:"tree,omitempty"`
	DNAMatches          []DNAMatchWithName          `json:"dna_matches"`
	SharedSegments      []database.SharedSegment    `json:"shared_segments"`
	FamilyRelationships []database.IndividualFamily `json:"family_relationships"`
	Surnames            []database.Surname          `json:"surnames"`
	Places              []database.Place            `json:"places"`
	Stats               IndividualStatsDetail       `json:"stats"`
}

// IndividualStatsDetail represents statistics for an individual
type IndividualStatsDetail struct {
	TotalMatches     int `json:"total_matches"`
	TotalSegments    int `json:"total_segments"`
	TotalSurnames    int `json:"total_surnames"`
	TotalPlaces      int `json:"total_places"`
	TotalFamilyRoles int `json:"total_family_roles"`
}

// SegmentsResponse represents filtered DNA segments
type SegmentsResponse struct {
	Segments []SegmentData `json:"segments"`
	Total    int64         `json:"total"`
	Page     int           `json:"page"`
	Limit    int           `json:"limit"`
}

// SegmentData represents a DNA segment record
type SegmentData struct {
	ID         string  `json:"id"`
	MatchID    string  `json:"match_id"`
	Chromosome string  `json:"chromosome"`
	StartPos   int64   `json:"start_pos"`
	EndPos     int64   `json:"end_pos"`
	Length     float64 `json:"length"`
}
