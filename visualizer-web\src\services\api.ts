import axios from 'axios';

// API Base URL - directly call API server
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:1231';

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API Types
export interface OverviewStats {
  total_dna_matches: number;
  total_individuals: number;
  total_trees: number;
  total_shared_segments: number;
  total_submitters: number;
  total_families: number;
  total_surnames: number;
  last_updated: string;
}

export interface MatchStats {
  total_matches: number;
  avg_shared_cm: number;
  max_shared_cm: number;
  min_shared_cm: number;
  matches_by_confidence: Record<string, number>;
  matches_by_relationship: Record<string, number>;
  cm_distribution: Array<{
    min_cm: number;
    max_cm: number;
    count: number;
  }>;
}

export interface CountryStats {
  country: string;
  count: number;
}

export interface IndividualStats {
  total_individuals: number;
  individuals_with_trees: number;
  gender_distribution: Record<string, number>;
  age_group_distribution: Record<string, number>;
}

export interface Individual {
  ID: string;
  FirstName: string;
  FirstNameTransliterated?: string;
  Name: string;
  NameTransliterated?: string;
  Gender?: string;
  AgeGroup?: string;
  AgeGroupInYears?: string;
  LinkInPedigreeTree?: string;
  LinkInTree?: string;
  RelationshipDescription?: string;
  BirthPlace?: string;
  TreeID?: string;
  LastName?: string;
  FormattedLastName?: string;
  IsAlive?: boolean;
  BirthDateYear?: string;
  DeathDateYear?: string;
}

export interface Tree {
  ID: string;
  Name: string;
  Link?: string;
  IndividualCount: number;
  SiteIsRequestMembershipAllowed: boolean;
  SiteCreatorID?: string;
  SiteCreatorName?: string;
  SiteCreatorNameTransliterated?: string;
  SiteCreatorCountry?: string;
  SiteCreatorCountryCode?: string;
  SiteCreatorLink?: string;
  SiteCreatorIsPublic: boolean;
}

export interface DNAMatch {
  ID: string;
  Link?: string;
  TotalSharedSegmentsLengthInCm: number;
  LargestSharedSegmentLengthInCm?: number;
  PercentageOfSharedSegments: number;
  TotalSharedSegments?: number;
  ConfidenceLevel?: string;
  ExactDnaRelationship?: string;
  GenealogicalRelationship?: string;
  IsRecentlyRecalculated?: boolean;
  CreatedAt: string;
  SubmitterID?: string;
  MatchIndividualID?: string;
  TreeID?: string;
  SourceIndividualID?: string;
  SharedMatchesCount?: number;
  UpdatedAt?: string;
  SurnamesCount?: number;
  PlacesCount?: number;
  SharedSegmentsCount?: number;
  PedigreeProcessed?: boolean;
  // Enhanced fields for matched individual information
  matched_individual_id?: string;
  matched_individual_name?: string;
}

export interface SharedSegment {
  ID: number;
  MatchID: string;
  ChromosomeID: number;
  StartPosition: number;
  EndPosition: number;
  StartRsid: string;
  EndRsid: string;
  LengthInCentimorgans: number;
  SnpCount: number;
}

export interface IndividualFamily {
  ID: number;
  FamilyID: string;
  IndividualID: string;
  Role: string;
}

export interface Surname {
  ID: number;
  Surname: string;
}

export interface Place {
  ID: number;
  Country?: string;
  StateOrProvince?: string;
  CountryCode?: string;
  StateOrProvinceCode?: string;
}

export interface IndividualStatsDetail {
  total_matches: number;
  total_segments: number;
  total_surnames: number;
  total_places: number;
  total_family_roles: number;
}

export interface IndividualDetailsResponse {
  individual: Individual;
  tree?: Tree;
  dna_matches: DNAMatch[];
  shared_segments: SharedSegment[];
  family_relationships: IndividualFamily[];
  surnames: Surname[];
  places: Place[];
  stats: IndividualStatsDetail;
}

export interface SurnameDetailsResponse {
  surname: Surname;
  individuals: Individual[];
  places: Place[];
  trees: Tree[];
  stats: SurnameStatsDetail;
}

export interface SurnameStatsDetail {
  total_individuals: number;
  total_places: number;
  total_trees: number;
  gender_distribution: { [key: string]: number };
  age_group_distribution: { [key: string]: number };
}

export interface PlaceDetailsResponse {
  place: Place;
  individuals: Individual[];
  surnames: Surname[];
  trees: Tree[];
  stats: PlaceStatsDetail;
}

export interface PlaceStatsDetail {
  total_individuals: number;
  total_surnames: number;
  total_trees: number;
  gender_distribution: { [key: string]: number };
  age_group_distribution: { [key: string]: number };
}

export interface NetworkNode {
  id: string;
  label: string;
  type: string;
  // Optional fields for visualization
  name?: string;
  group?: string;
  size?: number;
  color?: string;
}

export interface NetworkEdge {
  source: string;
  target: string;
  weight: number;
  relationship: string;
  // Optional field for visualization
  value?: number;
}

export interface NetworkGraph {
  nodes: NetworkNode[];
  edges: NetworkEdge[];
  stats: {
    node_count: number;
    edge_count: number;
    min_cm: number;
    depth: number;
  };
}

export interface Clique {
  id: number;
  members: string[];
  size: number;
  avg_cm: number;
  total_connections: number;
}

export interface CliquesResponse {
  cliques: Clique[];
  stats: {
    total_cliques: number;
    avg_size: number;
    max_size: number;
    min_size: number;
  };
}

// API Functions
export const analyticsApi = {
  // Overview
  getOverview: (): Promise<OverviewStats> =>
    api.get('/api/v1/analytics/overview').then(res => res.data),

  // Match Statistics
  getMatchStats: (): Promise<MatchStats> =>
    api.get('/api/v1/analytics/matches/stats').then(res => res.data),

  // Geography
  getTopCountries: (limit = 10): Promise<CountryStats[]> =>
    api.get(`/api/v1/analytics/geography/top-countries?limit=${limit}`).then(res => res.data.countries),

  // Individual Statistics
  getIndividualStats: (): Promise<IndividualStats> =>
    api.get('/api/v1/analytics/individuals/stats').then(res => res.data),

  // Network Graph
  getNetworkGraph: (params: {
    depth?: number;
    min_cm?: number;
    limit?: number;
  } = {}): Promise<NetworkGraph> => {
    const queryParams = new URLSearchParams();
    if (params.depth) queryParams.append('depth', params.depth.toString());
    if (params.min_cm) queryParams.append('min_cm', params.min_cm.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    return api.get(`/api/v1/analytics/network/graph?${queryParams}`).then(res => res.data);
  },

  // Network Cliques
  getNetworkCliques: (params: {
    min_size?: number;
    min_cm?: number;
    limit?: number;
    max_individuals?: number;
  } = {}): Promise<CliquesResponse> => {
    const queryParams = new URLSearchParams();
    if (params.min_size) queryParams.append('min_size', params.min_size.toString());
    if (params.min_cm) queryParams.append('min_cm', params.min_cm.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.max_individuals) queryParams.append('max_individuals', params.max_individuals.toString());

    return api.get(`/api/v1/analytics/network/cliques?${queryParams}`).then(res => res.data);
  },

  // Data endpoints with filtering
  getMatches: (params: {
    limit?: number;
    offset?: number;
    min_cm?: number;
    max_cm?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());
    if (params.min_cm) queryParams.append('min_cm', params.min_cm.toString());
    if (params.max_cm) queryParams.append('max_cm', params.max_cm.toString());

    return api.get(`/api/v1/analytics/matches?${queryParams}`).then(res => res.data);
  },

  getIndividuals: (params: {
    limit?: number;
    offset?: number;
    name?: string;
  } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());
    if (params.name) queryParams.append('name', params.name);

    return api.get(`/api/v1/analytics/individuals?${queryParams}`).then(res => res.data);
  },

  // Individual Details (legacy - full data)
  getIndividualDetails: (id: string): Promise<IndividualDetailsResponse> =>
    api.get(`/api/v1/analytics/individuals/${id}`).then(res => res.data),

  // Individual Details (progressive loading - separate endpoints)
  getIndividualBasicInfo: (id: string): Promise<{ individual: Individual; tree?: Tree }> =>
    api.get(`/api/v1/analytics/individuals/${id}/basic`).then(res => res.data),

  getIndividualDNAMatches: (id: string): Promise<{ dna_matches: DNAMatch[]; stats: { total_matches: number } }> =>
    api.get(`/api/v1/analytics/individuals/${id}/dna-matches`).then(res => res.data),

  getIndividualSharedSegments: (id: string): Promise<{ shared_segments: SharedSegment[]; stats: { total_segments: number } }> =>
    api.get(`/api/v1/analytics/individuals/${id}/shared-segments`).then(res => res.data),

  getIndividualFamily: (id: string): Promise<{ family_relationships: IndividualFamily[]; stats: { total_family_roles: number } }> =>
    api.get(`/api/v1/analytics/individuals/${id}/family`).then(res => res.data),

  getIndividualSurnames: (id: string): Promise<{ surnames: Surname[]; stats: { total_surnames: number } }> =>
    api.get(`/api/v1/analytics/individuals/${id}/surnames`).then(res => res.data),

  getIndividualPlaces: (id: string): Promise<{ places: Place[]; stats: { total_places: number } }> =>
    api.get(`/api/v1/analytics/individuals/${id}/places`).then(res => res.data),

  getSurnameDetails: (id: string): Promise<SurnameDetailsResponse> =>
    api.get(`/api/v1/analytics/surnames/${id}`).then(res => res.data),

  getPlaceDetails: (id: string): Promise<PlaceDetailsResponse> =>
    api.get(`/api/v1/analytics/places/${id}`).then(res => res.data),

  getSegments: (params: {
    limit?: number;
    offset?: number;
    chromosome?: number;
    min_length?: number;
  } = {}) => {
    const queryParams = new URLSearchParams();
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());
    if (params.chromosome) queryParams.append('chromosome', params.chromosome.toString());
    if (params.min_length) queryParams.append('min_length', params.min_length.toString());

    return api.get(`/api/v1/analytics/segments?${queryParams}`).then(res => res.data);
  },
};

// Export individual functions for easier imports
export const { getIndividualDetails, getSurnameDetails, getPlaceDetails } = analyticsApi;
