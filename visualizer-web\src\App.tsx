import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box } from '@mui/material';

// Components
import Navbar from './components/Layout/Navbar';
import Sidebar from './components/Layout/Sidebar';

// Pages
import Dashboard from './pages/Dashboard/Dashboard';
import DNAMatches from './pages/DNAMatches/DNAMatches';
import Individuals from './pages/Individuals/Individuals';
import IndividualDetail from './pages/IndividualDetail/IndividualDetail';
import SurnameDetail from './pages/SurnameDetail/SurnameDetail';
import PlaceDetail from './pages/PlaceDetail/PlaceDetail';
import NetworkGraph from './pages/NetworkGraph/NetworkGraph';
import Geography from './pages/Geography/Geography';
import Segments from './pages/Segments/Segments';
import Analytics from './pages/Analytics/Analytics';
import Cliques from './pages/Cliques/Cliques'; // DNA Cliques Analysis

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Create theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
  },
});

const App: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = React.useState(true);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Box sx={{ display: 'flex' }}>
            <Navbar onSidebarToggle={handleSidebarToggle} />
            <Sidebar open={sidebarOpen} onToggle={handleSidebarToggle} />
            <Box
              component="main"
              sx={{
                flexGrow: 1,
                p: 3,
                mt: 8, // Account for navbar height
                ml: sidebarOpen ? '240px' : '60px',
                transition: 'margin-left 0.3s',
              }}
            >
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/dna-matches" element={<DNAMatches />} />
                <Route path="/individuals" element={<Individuals />} />
                <Route path="/individuals/:id" element={<IndividualDetail />} />
                <Route path="/surnames/:id" element={<SurnameDetail />} />
                <Route path="/places/:id" element={<PlaceDetail />} />
                <Route path="/network" element={<NetworkGraph />} />
                <Route path="/geography" element={<Geography />} />
                <Route path="/segments" element={<Segments />} />
                <Route path="/analytics" element={<Analytics />} />
                <Route path="/cliques" element={<Cliques />} />
              </Routes>
            </Box>
          </Box>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
