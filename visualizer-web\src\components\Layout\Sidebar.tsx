import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Tooltip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Public as PublicIcon,
  Timeline as TimelineIcon,
  Analytics as AnalyticsIcon,
  Hub as HubIcon,
  Biotech as BiotechIcon,
  GroupWork as GroupWorkIcon,
} from '@mui/icons-material';

interface SidebarProps {
  open: boolean;
  onToggle: () => void;
}

const menuItems = [
  {
    text: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/dashboard',
    description: 'Overview and key metrics',
  },
  {
    text: 'DNA Matches',
    icon: <BiotechIcon />,
    path: '/dna-matches',
    description: 'DNA match analysis and statistics',
  },
  {
    text: 'Individuals',
    icon: <PeopleIcon />,
    path: '/individuals',
    description: 'Individual profiles and demographics',
  },
  {
    text: 'Network Graph',
    icon: <HubIcon />,
    path: '/network',
    description: 'Interactive network visualization',
  },
  {
    text: 'DNA Cliques',
    icon: <GroupWorkIcon />,
    path: '/cliques',
    description: 'Groups of fully connected individuals',
  },
  {
    text: 'Geography',
    icon: <PublicIcon />,
    path: '/geography',
    description: 'Geographic distribution and maps',
  },
  {
    text: 'DNA Segments',
    icon: <TimelineIcon />,
    path: '/segments',
    description: 'DNA segment analysis',
  },
  {
    text: 'Analytics',
    icon: <AnalyticsIcon />,
    path: '/analytics',
    description: 'Advanced analytics and insights',
  },
];

const Sidebar: React.FC<SidebarProps> = ({ open, onToggle }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const drawerWidth = open ? 240 : 60;

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          transition: 'width 0.3s',
          overflowX: 'hidden',
          mt: 8, // Account for navbar height
        },
      }}
    >
      <Box sx={{ overflow: 'auto', height: '100%' }}>
        <List>
          {menuItems.map((item) => {
            const isActive = location.pathname === item.path;

            return (
              <Tooltip
                key={item.text}
                title={open ? '' : item.description}
                placement="right"
                arrow
              >
                <ListItem disablePadding>
                  <ListItemButton
                    onClick={() => handleNavigation(item.path)}
                    selected={isActive}
                    sx={{
                      minHeight: 48,
                      justifyContent: open ? 'initial' : 'center',
                      px: 2.5,
                      '&.Mui-selected': {
                        backgroundColor: 'primary.main',
                        color: 'white',
                        '&:hover': {
                          backgroundColor: 'primary.dark',
                        },
                        '& .MuiListItemIcon-root': {
                          color: 'white',
                        },
                      },
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: 0,
                        mr: open ? 3 : 'auto',
                        justifyContent: 'center',
                        color: isActive ? 'white' : 'inherit',
                      }}
                    >
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.text}
                      sx={{
                        opacity: open ? 1 : 0,
                        transition: 'opacity 0.3s',
                      }}
                    />
                  </ListItemButton>
                </ListItem>
              </Tooltip>
            );
          })}
        </List>

        <Divider sx={{ my: 1 }} />

        {/* Additional info when expanded */}
        {open && (
          <Box sx={{ p: 2, mt: 'auto' }}>
            <Box
              sx={{
                backgroundColor: 'grey.100',
                borderRadius: 1,
                p: 1,
                textAlign: 'center',
              }}
            >
              <Box sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                MyHeritage DNA
              </Box>
              <Box sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                Database Visualizer
              </Box>
              <Box sx={{ fontSize: '0.7rem', color: 'text.disabled', mt: 0.5 }}>
                v1.0.0
              </Box>
            </Box>
          </Box>
        )}
      </Box>
    </Drawer>
  );
};

export default Sidebar;
