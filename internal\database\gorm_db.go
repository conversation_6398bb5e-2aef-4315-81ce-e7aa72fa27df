package database

import (
	"fmt"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// GormDB is a wrapper around gorm.DB
type GormDB struct {
	DB *gorm.DB
}

// NewGormDB creates a new GormDB instance based on the DB_TYPE environment variable
func NewGormDB(connStr ConnString) (*GormDB, error) {
	dbType := getEnv("DB_TYPE", "postgres")
	var db *gorm.DB
	var err error

	// Configure GORM logger
	logLevel := logger.Info
	if getEnv("DB_LOG_LEVEL", "info") == "silent" {
		logLevel = logger.Silent
	}

	config := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	}

	switch dbType {
	case "mariadb", "mysql":
		db, err = gorm.Open(mysql.Open(string(connStr)), config)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to MariaDB: %w", err)
		}
	case "postgres", "postgresql":
		db, err = gorm.Open(postgres.Open(string(connStr)), config)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
		}
	default:
		// Default to PostgreSQL
		db, err = gorm.Open(postgres.Open(string(connStr)), config)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
		}
	}

	return &GormDB{DB: db}, nil
}

// GetDB returns the underlying gorm.DB instance
func (g *GormDB) GetDB() *gorm.DB {
	return g.DB
}

// Close closes the database connection
func (g *GormDB) Close() error {
	sqlDB, err := g.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// Define models for our database tables
// These will be used by GORM for database operations

// ApiCall represents the api_calls table
type ApiCall struct {
	ID        uint   `gorm:"primaryKey"`
	URL       string `gorm:"column:url"`
	Response  string `gorm:"column:response;type:text"`
	CreatedAt string `gorm:"column:created_at"`
	Query     string `gorm:"column:query;type:text"`
}

// TableName overrides the table name
func (ApiCall) TableName() string {
	return "api_calls"
}

// DNAMatch represents the dna_matches table
type DNAMatch struct {
	ID                             string  `gorm:"primaryKey;column:id"`
	SourceIndividualID             string  `gorm:"column:source_individual_id"`
	Link                           string  `gorm:"column:link"`
	TotalSharedSegmentsLengthInCm  float64 `gorm:"column:total_shared_segments_length_in_cm"`
	LargestSharedSegmentLengthInCm float64 `gorm:"column:largest_shared_segment_length_in_cm"`
	PercentageOfSharedSegments     float64 `gorm:"column:percentage_of_shared_segments"`
	TotalSharedSegments            int     `gorm:"column:total_shared_segments"`
	ConfidenceLevel                string  `gorm:"column:confidence_level"`
	ExactDnaRelationship           string  `gorm:"column:exact_dna_relationship"`
	GenealogicalRelationship       string  `gorm:"column:genealogical_relationship"`
	IsRecentlyRecalculated         bool    `gorm:"column:is_recently_recalculated"`
	CreatedAt                      string  `gorm:"column:created_at"`
	SubmitterID                    string  `gorm:"column:submitter_id"`
	MatchIndividualID              string  `gorm:"column:match_individual_id"`
	TreeID                         string  `gorm:"column:tree_id"`
}

// TableName overrides the table name
func (DNAMatch) TableName() string {
	return "dna_matches"
}

// Submitter represents the submitters table
type Submitter struct {
	ID                      string `gorm:"primaryKey;column:id"`
	Name                    string `gorm:"column:name"`
	NameTransliterated      string `gorm:"column:name_transliterated"`
	FirstName               string `gorm:"column:first_name"`
	FirstNameTransliterated string `gorm:"column:first_name_transliterated"`
	Link                    string `gorm:"column:link"`
	IsPublic                bool   `gorm:"column:is_public"`
}

// TableName overrides the table name
func (Submitter) TableName() string {
	return "submitters"
}

// Individual represents the individuals table
type Individual struct {
	ID                      string `gorm:"primaryKey;column:id"`
	FirstName               string `gorm:"column:first_name"`
	FirstNameTransliterated string `gorm:"column:first_name_transliterated"`
	Name                    string `gorm:"column:name"`
	NameTransliterated      string `gorm:"column:name_transliterated"`
	Gender                  string `gorm:"column:gender"`
	AgeGroup                string `gorm:"column:age_group"`
	AgeGroupInYears         string `gorm:"column:age_group_in_years"`
	LinkInPedigreeTree      string `gorm:"column:link_in_pedigree_tree"`
	LinkInTree              string `gorm:"column:link_in_tree"`
	BirthPlace              string `gorm:"column:birth_place"`
	TreeID                  string `gorm:"column:tree_id"`
	BirthDateYear           string `gorm:"column:birth_date_year"`
}

// TableName overrides the table name
func (Individual) TableName() string {
	return "individuals"
}

// Tree represents the trees table
type Tree struct {
	ID                             string `gorm:"primaryKey;column:id"`
	Name                           string `gorm:"column:name"`
	Link                           string `gorm:"column:link"`
	IndividualCount                int    `gorm:"column:individual_count"`
	SiteIsRequestMembershipAllowed bool   `gorm:"column:site_is_request_membership_allowed"`
	SiteCreatorID                  string `gorm:"column:site_creator_id"`
	SiteCreatorName                string `gorm:"column:site_creator_name"`
	SiteCreatorNameTransliterated  string `gorm:"column:site_creator_name_transliterated"`
	SiteCreatorCountry             string `gorm:"column:site_creator_country"`
	SiteCreatorCountryCode         string `gorm:"column:site_creator_country_code"`
	SiteCreatorLink                string `gorm:"column:site_creator_link"`
	SiteCreatorIsPublic            bool   `gorm:"column:site_creator_is_public"`
}

// TableName overrides the table name
func (Tree) TableName() string {
	return "trees"
}

// ProcessedDnaKit represents the processed_dna_kits table
type ProcessedDnaKit struct {
	ID          int    `gorm:"primaryKey;column:id"`
	DnaKitID    string `gorm:"column:dnaKitID;uniqueIndex"`
	ProcessedAt string `gorm:"column:processed_at"`
}

// TableName overrides the table name
func (ProcessedDnaKit) TableName() string {
	return "processed_dna_kits"
}

// Token represents the tokens table
type Token struct {
	ID    int    `gorm:"primaryKey;column:id"`
	Token string `gorm:"column:token"`
}

// TableName overrides the table name
func (Token) TableName() string {
	return "tokens"
}

// SharedSegment represents the shared_segment table
type SharedSegment struct {
	ID                   int     `gorm:"primaryKey;column:id"`
	MatchID              string  `gorm:"column:match_id"`
	ChromosomeID         int     `gorm:"column:chromosome_id"`
	StartPosition        int     `gorm:"column:start_position"`
	EndPosition          int     `gorm:"column:end_position"`
	StartRsid            string  `gorm:"column:start_rsid"`
	EndRsid              string  `gorm:"column:end_rsid"`
	LengthInCentimorgans float64 `gorm:"column:length_in_centimorgans"`
	SnpCount             int     `gorm:"column:snp_count"`
}

// TableName overrides the table name
func (SharedSegment) TableName() string {
	return "shared_segment"
}

// Family represents the family table
type Family struct {
	ID string `gorm:"primaryKey;column:id"`
}

// TableName overrides the table name
func (Family) TableName() string {
	return "family"
}

// Surname represents the surname table
type Surname struct {
	ID      int    `gorm:"primaryKey;column:id"`
	Surname string `gorm:"column:surname"`
}

// TableName overrides the table name
func (Surname) TableName() string {
	return "surname"
}

// IndividualFamily represents the individual_family table
type IndividualFamily struct {
	ID           int    `gorm:"primaryKey;column:id"`
	FamilyID     string `gorm:"column:family_id"`
	IndividualID string `gorm:"column:individual_id"`
	Role         string `gorm:"column:role"`
}

// TableName overrides the table name
func (IndividualFamily) TableName() string {
	return "individual_family"
}

// Place represents the place table
type Place struct {
	ID                  int    `gorm:"primaryKey;column:id"`
	Country             string `gorm:"column:country"`
	StateOrProvince     string `gorm:"column:state_or_province"`
	CountryCode         string `gorm:"column:country_code"`
	StateOrProvinceCode string `gorm:"column:state_or_province_code"`
}

// TableName overrides the table name
func (Place) TableName() string {
	return "place"
}

// DNAClique represents a pre-computed clique of individuals
type DNAClique struct {
	ID               uint      `gorm:"primaryKey" json:"id"`
	Size             int       `gorm:"column:size;not null" json:"size"`
	MinCM            float64   `gorm:"column:min_cm;not null" json:"min_cm"`
	AvgCM            float64   `gorm:"column:avg_cm;not null" json:"avg_cm"`
	MaxCM            float64   `gorm:"column:max_cm;not null" json:"max_cm"`
	TotalConnections int       `gorm:"column:total_connections;not null" json:"total_connections"`
	Members          string    `gorm:"column:members;type:text;not null" json:"members"` // JSON array of individual IDs
	CreatedAt        time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// TableName overrides the table name
func (DNAClique) TableName() string {
	return "dna_cliques"
}

// DNACliqueRun represents a clique scanning run
type DNACliqueRun struct {
	ID               uint       `gorm:"primaryKey" json:"id"`
	Status           string     `gorm:"column:status;not null" json:"status"` // running, completed, failed
	MinCM            float64    `gorm:"column:min_cm;not null" json:"min_cm"`
	MinSize          int        `gorm:"column:min_size;not null" json:"min_size"`
	MaxIndividuals   int        `gorm:"column:max_individuals;not null" json:"max_individuals"`
	TotalIndividuals int        `gorm:"column:total_individuals" json:"total_individuals"`
	TotalCliques     int        `gorm:"column:total_cliques" json:"total_cliques"`
	ProcessingTimeMs int64      `gorm:"column:processing_time_ms" json:"processing_time_ms"`
	ErrorMessage     string     `gorm:"column:error_message;type:text" json:"error_message"`
	StartedAt        time.Time  `gorm:"column:started_at" json:"started_at"`
	CompletedAt      *time.Time `gorm:"column:completed_at" json:"completed_at"`
	CreatedAt        time.Time  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt        time.Time  `gorm:"column:updated_at" json:"updated_at"`
}

// TableName overrides the table name
func (DNACliqueRun) TableName() string {
	return "dna_clique_runs"
}
