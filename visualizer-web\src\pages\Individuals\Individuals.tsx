import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  IconButton,
  Tooltip,
  Link,
} from '@mui/material';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { useQuery } from '@tanstack/react-query';
import { analyticsApi, IndividualStats } from '../../services/api';
import RefreshIcon from '@mui/icons-material/Refresh';
import PeopleIcon from '@mui/icons-material/People';
import WomanIcon from '@mui/icons-material/Woman';
import ManIcon from '@mui/icons-material/Man';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';

// Color palette for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

// Updated table structure: ID | Name | Birth Year

interface IndividualData {
  id: string;
  name: string;
  birth_year: string;
}

interface IndividualsResponse {
  individuals: IndividualData[];
  total: number;
  page: number;
  limit: number;
}

const Individuals: React.FC = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [nameFilter, setNameFilter] = useState('');

  // Fetch individual statistics
  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useQuery<IndividualStats>({
    queryKey: ['individualStats'],
    queryFn: analyticsApi.getIndividualStats,
    refetchOnWindowFocus: false,
  });

  // Fetch individual data with filters
  const {
    data: individualsData,
    isLoading: individualsLoading,
    error: individualsError,
    refetch: refetchIndividuals,
  } = useQuery<IndividualsResponse>({
    queryKey: ['individuals', page, rowsPerPage, nameFilter],
    queryFn: () => analyticsApi.getIndividuals({
      limit: rowsPerPage,
      offset: page * rowsPerPage,
      ...(nameFilter && { name: nameFilter }),
    }),
    refetchOnWindowFocus: false,
  });

  // Reset page to 0 if current page is out of range
  React.useEffect(() => {
    if (individualsData && individualsData.total > 0) {
      const maxPage = Math.max(0, Math.ceil(individualsData.total / rowsPerPage) - 1);
      if (page > maxPage) {
        setPage(0);
      }
    }
  }, [individualsData, page, rowsPerPage]);

  const handleRefresh = () => {
    refetchStats();
    refetchIndividuals();
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const clearFilters = () => {
    setNameFilter('');
    setPage(0);
  };

  const handleIndividualClick = (individualId: string) => {
    navigate(`/individuals/${individualId}`);
  };

  // Reset page when name filter changes
  React.useEffect(() => {
    setPage(0);
  }, [nameFilter]);

  // Prepare chart data - convert map to array for charts
  const genderChartData = React.useMemo(() => {
    if (!statsData?.gender_distribution) return [];

    const total = statsData.total_individuals;
    return Object.entries(statsData.gender_distribution).map(([gender, count], index) => ({
      name: gender || 'Unknown',
      value: count,
      percentage: total > 0 ? (count / total) * 100 : 0,
      color: COLORS[index % COLORS.length],
    }));
  }, [statsData]);

  const ageChartData = React.useMemo(() => {
    if (!statsData?.age_group_distribution) return [];

    const total = statsData.total_individuals;
    return Object.entries(statsData.age_group_distribution).map(([ageGroup, count]) => ({
      name: ageGroup || 'Unknown',
      count: count,
      percentage: total > 0 ? (count / total) * 100 : 0,
    }));
  }, [statsData]);

  if (statsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (statsError) {
    return (
      <Alert
        severity="error"
        action={
          <Chip label="Retry" onClick={handleRefresh} color="error" variant="outlined" />
        }
      >
        Failed to load individual statistics. Please check if the API server is running.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Individual Analysis
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Demographics, surname patterns, and individual profiles
          </Typography>
        </Box>
        <Tooltip title="Refresh Data">
          <IconButton onClick={handleRefresh} color="primary">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Statistics Overview */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PeopleIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Individuals</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {statsData?.total_individuals?.toLocaleString() || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <FamilyRestroomIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">With Trees</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {statsData?.individuals_with_trees?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {statsData && statsData.total_individuals > 0
                  ? ((statsData.individuals_with_trees / statsData.total_individuals) * 100).toFixed(1)
                  : 0}% of total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <ManIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Male</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {genderChartData.find(g => g.name.toLowerCase() === 'male')?.value?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {genderChartData.find(g => g.name.toLowerCase() === 'male')?.percentage?.toFixed(1) || 0}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <WomanIcon color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6">Female</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {genderChartData.find(g => g.name.toLowerCase() === 'female')?.value?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {genderChartData.find(g => g.name.toLowerCase() === 'female')?.percentage?.toFixed(1) || 0}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} mb={4}>
        {/* Gender Distribution */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Gender Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={genderChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name}: ${percentage?.toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {genderChartData.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <RechartsTooltip formatter={(value: any) => [value?.toLocaleString(), 'Count']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Age Distribution */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Age Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={ageChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip formatter={(value: any) => [value?.toLocaleString(), 'Count']} />
                <Legend />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Filters Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Filter Individuals
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              size="small"
              label="Name"
              value={nameFilter}
              onChange={(e) => setNameFilter(e.target.value)}
              placeholder="Enter name to search"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Button
              variant="outlined"
              onClick={clearFilters}
              fullWidth
              size="small"
            >
              Clear Filters
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={6}>
            <Box display="flex" gap={1} justifyContent="flex-end">
              <Chip
                label={`Total: ${individualsData?.total?.toLocaleString() || 0}`}
                color="primary"
                size="small"
              />
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Individuals Table */}
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 600 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Birth Year</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {individualsLoading ? (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    <CircularProgress size={40} />
                  </TableCell>
                </TableRow>
              ) : individualsError ? (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    <Alert severity="error">Failed to load individuals data</Alert>
                  </TableCell>
                </TableRow>
              ) : (
                individualsData?.individuals?.map((individual) => (
                  <TableRow key={individual.id} hover>
                    <TableCell>{individual.id}</TableCell>
                    <TableCell>
                      <Link
                        component="button"
                        variant="body1"
                        onClick={() => handleIndividualClick(individual.id)}
                        sx={{
                          textAlign: 'left',
                          textDecoration: 'none',
                          '&:hover': {
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        {individual.name || 'Unknown'}
                      </Link>
                    </TableCell>
                    <TableCell>{individual.birth_year || 'Unknown'}</TableCell>
                  </TableRow>
                )) || []
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={individualsData?.total || 0}
          rowsPerPage={rowsPerPage}
          page={Math.min(page, Math.max(0, Math.ceil((individualsData?.total || 0) / rowsPerPage) - 1))}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Box>
  );
};

export default Individuals;