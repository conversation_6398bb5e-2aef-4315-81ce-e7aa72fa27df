package main

import (
	"encoding/json"
	"fmt"
	"log"
	"sort"

	"github.com/dmagur/myheritage/internal/database"
)

// CliqueResult represents a processed clique ready for database storage
type CliqueResult struct {
	Members          []string
	Size             int
	MinCM            float64
	AvgCM            float64
	MaxCM            float64
	TotalConnections int
}

// getConnectedIndividuals returns individuals with at least one DNA match above minCM threshold
func (s *CliqueScanner) getConnectedIndividuals() ([]string, error) {
	var individuals []string

	query := `
		SELECT DISTINCT individual_id
		FROM (
			SELECT source_individual_id as individual_id
			FROM dna_matches
			WHERE total_shared_segments_length_in_cm >= ?
			UNION
			SELECT match_individual_id as individual_id
			FROM dna_matches
			WHERE total_shared_segments_length_in_cm >= ?
		) combined
		ORDER BY individual_id
		LIMIT ?
	`

	if err := s.db.Raw(query, s.args.MinCM, s.args.MinCM, s.args.MaxIndividuals).Scan(&individuals).Error; err != nil {
		return nil, err
	}

	if s.args.Verbose {
		log.Printf("Found %d individuals with matches >= %.1f cM", len(individuals), s.args.MinCM)
	}

	return individuals, nil
}

// buildAdjacencyMap creates a map of individual -> list of connected individuals
func (s *CliqueScanner) buildAdjacencyMap(individuals []string) (map[string][]string, error) {
	adjacencyMap := make(map[string][]string)

	// Initialize empty adjacency lists
	for _, individual := range individuals {
		adjacencyMap[individual] = []string{}
	}

	// Query all connections between the individuals
	var connections []struct {
		Source string `json:"source"`
		Target string `json:"target"`
	}

	query := `
		SELECT source_individual_id as source, match_individual_id as target
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= ?
		AND source_individual_id IN (?)
		AND match_individual_id IN (?)
	`

	if err := s.db.Raw(query, s.args.MinCM, individuals, individuals).Scan(&connections).Error; err != nil {
		return nil, err
	}

	if s.args.Verbose {
		log.Printf("Found %d connections between individuals", len(connections))
	}

	// Build bidirectional adjacency map
	for _, conn := range connections {
		adjacencyMap[conn.Source] = append(adjacencyMap[conn.Source], conn.Target)
		adjacencyMap[conn.Target] = append(adjacencyMap[conn.Target], conn.Source)
	}

	// Remove duplicates and sort
	for individual := range adjacencyMap {
		adjacencyMap[individual] = removeDuplicates(adjacencyMap[individual])
		sort.Strings(adjacencyMap[individual])
	}

	return adjacencyMap, nil
}

// findCliques implements the Bron-Kerbosch algorithm to find all maximal cliques
func (s *CliqueScanner) findCliques(individuals []string, adjacencyMap map[string][]string) [][]string {
	var allCliques [][]string
	
	if s.args.Verbose {
		log.Printf("Starting Bron-Kerbosch algorithm with %d individuals", len(individuals))
	}

	s.bronKerbosch([]string{}, individuals, []string{}, adjacencyMap, &allCliques)

	if s.args.Verbose {
		log.Printf("Bron-Kerbosch found %d maximal cliques", len(allCliques))
	}

	return allCliques
}

// bronKerbosch implements the Bron-Kerbosch algorithm for finding maximal cliques
func (s *CliqueScanner) bronKerbosch(r, p, x []string, adjacencyMap map[string][]string, cliques *[][]string) {
	if len(p) == 0 && len(x) == 0 {
		// Found a maximal clique
		clique := make([]string, len(r))
		copy(clique, r)
		*cliques = append(*cliques, clique)
		return
	}

	// Make a copy of p to iterate over
	pCopy := make([]string, len(p))
	copy(pCopy, p)

	for _, v := range pCopy {
		// R ∪ {v}
		newR := append(r, v)

		// P ∩ N(v)
		newP := intersection(p, adjacencyMap[v])

		// X ∩ N(v)
		newX := intersection(x, adjacencyMap[v])

		// Recursive call
		s.bronKerbosch(newR, newP, newX, adjacencyMap, cliques)

		// P := P \ {v}
		p = removeElement(p, v)

		// X := X ∪ {v}
		x = append(x, v)
	}
}

// processCliques filters cliques by minimum size and calculates statistics
func (s *CliqueScanner) processCliques(rawCliques [][]string) ([]CliqueResult, error) {
	var results []CliqueResult

	for _, clique := range rawCliques {
		if len(clique) < s.args.MinSize {
			continue
		}

		// Calculate statistics for this clique
		stats, err := s.calculateCliqueStats(clique)
		if err != nil {
			if s.args.Verbose {
				log.Printf("Failed to calculate stats for clique %v: %v", clique, err)
			}
			continue
		}

		result := CliqueResult{
			Members:          clique,
			Size:             len(clique),
			MinCM:            stats.MinCM,
			AvgCM:            stats.AvgCM,
			MaxCM:            stats.MaxCM,
			TotalConnections: len(clique) * (len(clique) - 1) / 2,
		}

		results = append(results, result)
	}

	// Sort by size (descending) then by average cM (descending)
	sort.Slice(results, func(i, j int) bool {
		if results[i].Size != results[j].Size {
			return results[i].Size > results[j].Size
		}
		return results[i].AvgCM > results[j].AvgCM
	})

	return results, nil
}

// CliqueStats holds statistical information about a clique
type CliqueStats struct {
	MinCM float64
	AvgCM float64
	MaxCM float64
	Count int
}

// calculateCliqueStats calculates statistical information for a clique
func (s *CliqueScanner) calculateCliqueStats(clique []string) (*CliqueStats, error) {
	if len(clique) < 2 {
		return &CliqueStats{}, nil
	}

	var stats struct {
		MinCM float64 `json:"min_cm"`
		AvgCM float64 `json:"avg_cm"`
		MaxCM float64 `json:"max_cm"`
		Count int     `json:"count"`
	}

	query := `
		SELECT 
			MIN(total_shared_segments_length_in_cm) as min_cm,
			AVG(total_shared_segments_length_in_cm) as avg_cm,
			MAX(total_shared_segments_length_in_cm) as max_cm,
			COUNT(*) as count
		FROM dna_matches
		WHERE total_shared_segments_length_in_cm >= ?
		AND source_individual_id IN (?)
		AND match_individual_id IN (?)
	`

	if err := s.db.Raw(query, s.args.MinCM, clique, clique).Scan(&stats).Error; err != nil {
		return nil, err
	}

	return &CliqueStats{
		MinCM: stats.MinCM,
		AvgCM: stats.AvgCM,
		MaxCM: stats.MaxCM,
		Count: stats.Count,
	}, nil
}

// saveCliques saves the processed cliques to the database
func (s *CliqueScanner) saveCliques(cliques []CliqueResult) error {
	// Clear existing cliques with the same parameters
	if err := s.clearExistingCliques(); err != nil {
		return fmt.Errorf("failed to clear existing cliques: %w", err)
	}

	// Save new cliques in batches
	batchSize := 100
	for i := 0; i < len(cliques); i += batchSize {
		end := i + batchSize
		if end > len(cliques) {
			end = len(cliques)
		}

		batch := cliques[i:end]
		if err := s.saveBatch(batch); err != nil {
			return fmt.Errorf("failed to save batch %d-%d: %w", i, end, err)
		}

		if s.args.Verbose {
			log.Printf("Saved batch %d-%d (%d cliques)", i+1, end, len(batch))
		}
	}

	log.Printf("Successfully saved %d cliques to database", len(cliques))
	return nil
}

// clearExistingCliques removes existing cliques with similar parameters
func (s *CliqueScanner) clearExistingCliques() error {
	// Delete cliques with the same min_cm and min_size to avoid duplicates
	result := s.db.Where("min_cm = ? AND size >= ?", s.args.MinCM, s.args.MinSize).Delete(&database.DNAClique{})
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected > 0 {
		log.Printf("Cleared %d existing cliques with min_cm=%.1f, min_size=%d", 
			result.RowsAffected, s.args.MinCM, s.args.MinSize)
	}

	return nil
}

// saveBatch saves a batch of cliques to the database
func (s *CliqueScanner) saveBatch(cliques []CliqueResult) error {
	var dbCliques []database.DNAClique

	for _, clique := range cliques {
		membersJSON, err := json.Marshal(clique.Members)
		if err != nil {
			return fmt.Errorf("failed to marshal members for clique: %w", err)
		}

		dbClique := database.DNAClique{
			Size:             clique.Size,
			MinCM:            clique.MinCM,
			AvgCM:            clique.AvgCM,
			MaxCM:            clique.MaxCM,
			TotalConnections: clique.TotalConnections,
			Members:          string(membersJSON),
		}

		dbCliques = append(dbCliques, dbClique)
	}

	return s.db.Create(&dbCliques).Error
}

// Utility functions

// intersection returns the intersection of two string slices
func intersection(slice1, slice2 []string) []string {
	set := make(map[string]bool)
	for _, item := range slice1 {
		set[item] = true
	}

	var result []string
	for _, item := range slice2 {
		if set[item] {
			result = append(result, item)
		}
	}

	return result
}

// removeElement removes an element from a string slice
func removeElement(slice []string, element string) []string {
	var result []string
	for _, item := range slice {
		if item != element {
			result = append(result, item)
		}
	}
	return result
}

// removeDuplicates removes duplicate strings from a slice
func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}
