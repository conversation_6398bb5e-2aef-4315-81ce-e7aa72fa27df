import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  Slider,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
} from 'recharts';
import { useQuery } from '@tanstack/react-query';
import { analyticsApi } from '../../services/api';
import RefreshIcon from '@mui/icons-material/Refresh';
import DnaIcon from '@mui/icons-material/Biotech';
import TimelineIcon from '@mui/icons-material/Timeline';

interface SegmentData {
  id: string;
  match_id: string;
  chromosome: number;
  start_position: number;
  end_position: number;
  length_cm: number;
  snp_count: number;
}

interface SegmentsResponse {
  segments: SegmentData[];
  total: number;
  page: number;
  limit: number;
}

const Segments: React.FC = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [matchIdFilter, setMatchIdFilter] = useState('');
  const [chromosomeFilter, setChromosomeFilter] = useState('');
  const [minLengthFilter, setMinLengthFilter] = useState([0, 100]);

  // Fetch segments data with filters
  const {
    data: segmentsData,
    isLoading: segmentsLoading,
    error: segmentsError,
    refetch: refetchSegments,
  } = useQuery<SegmentsResponse>({
    queryKey: ['segments', page, rowsPerPage, matchIdFilter, chromosomeFilter, minLengthFilter[0]],
    queryFn: () => analyticsApi.getSegments({
      limit: rowsPerPage,
      offset: page * rowsPerPage,
      ...(matchIdFilter && { match_id: matchIdFilter }),
      ...(chromosomeFilter && { chromosome: parseInt(chromosomeFilter) }),
      ...(minLengthFilter[0] > 0 && { min_length: minLengthFilter[0] }),
    }),
    refetchOnWindowFocus: false,
  });

  const handleRefresh = () => {
    refetchSegments();
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const clearFilters = () => {
    setMatchIdFilter('');
    setChromosomeFilter('');
    setMinLengthFilter([0, 100]);
    setPage(0);
  };

  // Prepare chromosome distribution data
  const chromosomeDistribution = React.useMemo(() => {
    if (!segmentsData?.segments) return [];

    const chrCounts: { [key: number]: number } = {};
    segmentsData.segments.forEach(segment => {
      chrCounts[segment.chromosome] = (chrCounts[segment.chromosome] || 0) + 1;
    });

    return Object.entries(chrCounts)
      .map(([chr, count]) => ({
        chromosome: `Chr ${chr}`,
        count,
      }))
      .sort((a, b) => parseInt(a.chromosome.split(' ')[1]) - parseInt(b.chromosome.split(' ')[1]));
  }, [segmentsData]);

  // Prepare length distribution data
  const lengthDistribution = React.useMemo(() => {
    if (!segmentsData?.segments) return [];

    const buckets = [
      { range: '0-5 cM', min: 0, max: 5, count: 0 },
      { range: '5-10 cM', min: 5, max: 10, count: 0 },
      { range: '10-20 cM', min: 10, max: 20, count: 0 },
      { range: '20-50 cM', min: 20, max: 50, count: 0 },
      { range: '50+ cM', min: 50, max: Infinity, count: 0 },
    ];

    segmentsData.segments.forEach(segment => {
      const bucket = buckets.find(b => segment.length_cm >= b.min && segment.length_cm < b.max);
      if (bucket) bucket.count++;
    });

    return buckets;
  }, [segmentsData]);

  // Calculate statistics
  const stats = React.useMemo(() => {
    if (!segmentsData?.segments?.length) return null;

    const lengths = segmentsData.segments.map(s => s.length_cm);
    const totalLength = lengths.reduce((sum, len) => sum + len, 0);
    const avgLength = totalLength / lengths.length;
    const maxLength = Math.max(...lengths);
    const minLength = Math.min(...lengths);

    return {
      totalSegments: segmentsData.segments.length,
      totalLength: totalLength.toFixed(2),
      avgLength: avgLength.toFixed(2),
      maxLength: maxLength.toFixed(2),
      minLength: minLength.toFixed(2),
    };
  }, [segmentsData]);

  if (segmentsLoading && !segmentsData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (segmentsError) {
    return (
      <Alert
        severity="error"
        action={
          <Chip label="Retry" onClick={handleRefresh} color="error" variant="outlined" />
        }
      >
        Failed to load DNA segments data. Please check if the API server is running.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            DNA Segment Analysis
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Chromosome browser, segment length distribution, and detailed analysis
          </Typography>
        </Box>
        <Tooltip title="Refresh Data">
          <IconButton onClick={handleRefresh} color="primary">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Statistics Overview */}
      {stats && (
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={2.4}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <DnaIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Total Segments</Typography>
                </Box>
                <Typography variant="h4" fontWeight="bold">
                  {stats.totalSegments.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2.4}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <TimelineIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6">Total Length</Typography>
                </Box>
                <Typography variant="h4" fontWeight="bold">
                  {stats.totalLength} cM
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2.4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Average Length</Typography>
                <Typography variant="h4" fontWeight="bold">
                  {stats.avgLength} cM
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2.4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Max Length</Typography>
                <Typography variant="h4" fontWeight="bold">
                  {stats.maxLength} cM
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2.4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Min Length</Typography>
                <Typography variant="h4" fontWeight="bold">
                  {stats.minLength} cM
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Charts Section */}
      <Grid container spacing={3} mb={4}>
        {/* Chromosome Distribution */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Chromosome Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chromosomeDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="chromosome" />
                <YAxis />
                <RechartsTooltip formatter={(value: any) => [value?.toLocaleString(), 'Segments']} />
                <Legend />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Length Distribution */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Segment Length Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={lengthDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="range" />
                <YAxis />
                <RechartsTooltip formatter={(value: any) => [value?.toLocaleString(), 'Segments']} />
                <Legend />
                <Bar dataKey="count" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Filters Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Filter DNA Segments
        </Typography>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              size="small"
              label="Match ID"
              value={matchIdFilter}
              onChange={(e) => setMatchIdFilter(e.target.value)}
              placeholder="Enter match ID"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Chromosome</InputLabel>
              <Select
                value={chromosomeFilter}
                label="Chromosome"
                onChange={(e) => setChromosomeFilter(e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                {Array.from({ length: 22 }, (_, i) => i + 1).map(chr => (
                  <MenuItem key={chr} value={chr.toString()}>{chr}</MenuItem>
                ))}
                <MenuItem value="23">X</MenuItem>
                <MenuItem value="24">Y</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Typography gutterBottom>
              Min Length: {minLengthFilter[0]} cM
            </Typography>
            <Slider
              value={minLengthFilter}
              onChange={(_, newValue) => setMinLengthFilter(newValue as number[])}
              valueLabelDisplay="auto"
              min={0}
              max={100}
              step={1}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Button
              variant="outlined"
              onClick={clearFilters}
              fullWidth
              size="small"
            >
              Clear Filters
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Box display="flex" gap={1}>
              <Chip
                label={`Total: ${segmentsData?.total?.toLocaleString() || 0}`}
                color="primary"
                size="small"
              />
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Segments Table */}
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 600 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>Segment ID</TableCell>
                <TableCell>Match ID</TableCell>
                <TableCell>Chromosome</TableCell>
                <TableCell>Start Position</TableCell>
                <TableCell>End Position</TableCell>
                <TableCell>Length (cM)</TableCell>
                <TableCell>SNP Count</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {segmentsLoading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <CircularProgress size={40} />
                  </TableCell>
                </TableRow>
              ) : segmentsError ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <Alert severity="error">Failed to load segments data</Alert>
                  </TableCell>
                </TableRow>
              ) : (
                segmentsData?.segments?.map((segment) => (
                  <TableRow key={segment.id} hover>
                    <TableCell>{segment.id}</TableCell>
                    <TableCell>
                      <Chip label={segment.match_id} size="small" color="primary" />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={segment.chromosome === 23 ? 'X' : segment.chromosome === 24 ? 'Y' : segment.chromosome}
                        size="small"
                        color="secondary"
                      />
                    </TableCell>
                    <TableCell>{segment.start_position?.toLocaleString()}</TableCell>
                    <TableCell>{segment.end_position?.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip
                        label={`${segment.length_cm?.toFixed(2)} cM`}
                        size="small"
                        color={segment.length_cm > 20 ? 'success' : segment.length_cm > 10 ? 'warning' : 'default'}
                      />
                    </TableCell>
                    <TableCell>{segment.snp_count?.toLocaleString()}</TableCell>
                  </TableRow>
                )) || []
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={segmentsData?.total || 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Box>
  );
};

export default Segments;
