import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,

  IconButton,
  Tooltip,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { analyticsApi, CliquesResponse } from '../../services/api';
import RefreshIcon from '@mui/icons-material/Refresh';
import GroupIcon from '@mui/icons-material/Group';
import ConnectWithoutContactIcon from '@mui/icons-material/ConnectWithoutContact';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

const Cliques: React.FC = () => {
  const [minSize, setMinSize] = useState(3);
  const [minCM, setMinCM] = useState(50);
  const [limit, setLimit] = useState(20);
  const [maxIndividuals, setMaxIndividuals] = useState(1000);

  // Fetch cliques data
  const {
    data: cliquesData,
    isLoading,
    error,
    refetch,
  } = useQuery<CliquesResponse>({
    queryKey: ['cliques', minSize, minCM, limit, maxIndividuals],
    queryFn: () => analyticsApi.getNetworkCliques({
      min_size: minSize,
      min_cm: minCM,
      limit: limit,
      max_individuals: maxIndividuals
    }),
    refetchOnWindowFocus: false,
  });

  const handleRefresh = () => {
    refetch();
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        action={
          <Chip label="Retry" onClick={handleRefresh} color="error" variant="outlined" />
        }
      >
        Failed to load cliques data. Please check if the API server is running.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            DNA Match Cliques
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Groups of individuals where each person has DNA matches with every other person in the group
          </Typography>
        </Box>
        <Tooltip title="Refresh Data">
          <IconButton onClick={handleRefresh} color="primary">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Statistics Overview */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <GroupIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Cliques</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {cliquesData?.stats?.total_cliques || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <ConnectWithoutContactIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Average Size</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {cliquesData?.stats?.avg_size?.toFixed(1) || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TrendingUpIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Largest Clique</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {cliquesData?.stats?.max_size || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                members
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <GroupIcon color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6">Smallest Clique</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {cliquesData?.stats?.min_size || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                members
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Filter Parameters
        </Typography>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <Typography gutterBottom>Minimum Clique Size: {minSize}</Typography>
            <Slider
              value={minSize}
              onChange={(_, value) => setMinSize(value as number)}
              min={2}
              max={10}
              step={1}
              marks
              valueLabelDisplay="auto"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Typography gutterBottom>Minimum cM: {minCM}</Typography>
            <Slider
              value={minCM}
              onChange={(_, value) => setMinCM(value as number)}
              min={10}
              max={200}
              step={10}
              marks={[
                { value: 10, label: '10' },
                { value: 50, label: '50' },
                { value: 100, label: '100' },
                { value: 200, label: '200' },
              ]}
              valueLabelDisplay="auto"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Max Results</InputLabel>
              <Select
                value={limit}
                label="Max Results"
                onChange={(e) => setLimit(e.target.value as number)}
              >
                <MenuItem value={10}>10</MenuItem>
                <MenuItem value={20}>20</MenuItem>
                <MenuItem value={50}>50</MenuItem>
                <MenuItem value={100}>100</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Max Individuals</InputLabel>
              <Select
                value={maxIndividuals}
                label="Max Individuals"
                onChange={(e) => setMaxIndividuals(e.target.value as number)}
              >
                <MenuItem value={500}>500</MenuItem>
                <MenuItem value={1000}>1,000</MenuItem>
                <MenuItem value={2000}>2,000</MenuItem>
                <MenuItem value={5000}>5,000</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Cliques Table */}
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer sx={{ maxHeight: 600 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Size</TableCell>
                <TableCell>Members</TableCell>
                <TableCell>Avg cM</TableCell>
                <TableCell>Connections</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {cliquesData?.cliques?.map((clique) => (
                <TableRow key={clique.id} hover>
                  <TableCell>{clique.id}</TableCell>
                  <TableCell>
                    <Chip
                      label={clique.size}
                      color="primary"
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ maxWidth: 400, overflow: 'hidden' }}>
                      {clique.members.slice(0, 3).map((member, index) => (
                        <Chip
                          key={member}
                          label={member.split('-').pop()}
                          size="small"
                          variant="outlined"
                          sx={{ mr: 0.5, mb: 0.5 }}
                        />
                      ))}
                      {clique.members.length > 3 && (
                        <Chip
                          label={`+${clique.members.length - 3} more`}
                          size="small"
                          color="secondary"
                          sx={{ mr: 0.5, mb: 0.5 }}
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {clique.avg_cm.toFixed(1)} cM
                    </Typography>
                  </TableCell>
                  <TableCell>{clique.total_connections}</TableCell>
                </TableRow>
              )) || []}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

export default Cliques;
