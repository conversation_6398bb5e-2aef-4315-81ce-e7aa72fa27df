#!/bin/bash

# DNA Clique Scanner Runner Script
# This script runs the clique scanner with configurable parameters

set -e

echo "🧬 DNA Clique Scanner"
echo "===================="

# Default values
MIN_CM=${MIN_CM:-50}
MIN_SIZE=${MIN_SIZE:-3}
MAX_INDIVIDUALS=${MAX_INDIVIDUALS:-1000}
DRY_RUN=${DRY_RUN:-false}
VERBOSE=${VERBOSE:-false}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --min-cm)
            MIN_CM="$2"
            shift 2
            ;;
        --min-size)
            MIN_SIZE="$2"
            shift 2
            ;;
        --max-individuals)
            MAX_INDIVIDUALS="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --verbose)
            VERBOSE="true"
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --min-cm VALUE          Minimum cM threshold (default: 50)"
            echo "  --min-size VALUE        Minimum clique size (default: 3)"
            echo "  --max-individuals VALUE Maximum individuals to process (default: 1000)"
            echo "  --dry-run               Don't save to database, just show results"
            echo "  --verbose               Enable verbose logging"
            echo "  --help                  Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --min-cm 100 --min-size 4"
            echo "  $0 --dry-run --verbose"
            echo "  $0 --max-individuals 2000 --min-cm 75"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "Configuration:"
echo "  Min cM threshold: $MIN_CM"
echo "  Min clique size: $MIN_SIZE"
echo "  Max individuals: $MAX_INDIVIDUALS"
echo "  Dry run: $DRY_RUN"
echo "  Verbose: $VERBOSE"
echo ""

# Check if we're in the right directory
if [ ! -f "go.mod" ]; then
    echo "❌ Error: go.mod not found. Please run this script from the project root directory."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found. Please create it with database configuration."
    exit 1
fi

# Export environment variables
export MIN_CM
export MIN_SIZE
export MAX_INDIVIDUALS
export DRY_RUN
export VERBOSE

# Load environment variables from .env file
set -a
source .env
set +a

echo "🚀 Starting clique scanner..."
echo ""

# Run the clique scanner
if command -v go &> /dev/null; then
    # Run directly with Go
    go run cmd/clique-scanner/main.go cmd/clique-scanner/scanner.go
else
    echo "❌ Error: Go not found. Please install Go or use Docker."
    exit 1
fi

echo ""
echo "✅ Clique scanner completed!"
