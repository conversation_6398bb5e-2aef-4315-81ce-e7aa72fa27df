package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/dmagur/myheritage/internal/database"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🚀 Database Performance Optimizer")
	fmt.Println("==================================")

	// Initialize database connection
	connStr := database.GetConnectionString()
	gormDB, err := database.NewGormDB(connStr)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("✅ Connected to database")

	// Apply critical indexes
	if err := applyCriticalIndexes(gormDB.GetDB()); err != nil {
		log.Fatalf("Failed to apply indexes: %v", err)
	}

	fmt.Println("🎉 Database optimization completed successfully!")
	fmt.Println("")
	fmt.Println("Expected performance improvements:")
	fmt.Println("• DNA matches queries: 50s → 1-3s")
	fmt.Println("• Surname popularity: 20s → 1-2s")
	fmt.Println("• Place popularity: 2s → 0.5-1s")
	fmt.Println("• Individual details API: 60s+ → 5-10s")
	fmt.Println("")
	fmt.Println("🔄 Please restart the API server to see the improvements")
}

func applyCriticalIndexes(db *gorm.DB) error {
	indexes := []struct {
		name  string
		query string
	}{
		{
			name:  "DNA matches - source individual",
			query: "CREATE INDEX IF NOT EXISTS idx_dna_matches_source_individual ON dna_matches(source_individual_id)",
		},
		{
			name:  "DNA matches - match individual",
			query: "CREATE INDEX IF NOT EXISTS idx_dna_matches_match_individual ON dna_matches(match_individual_id)",
		},
		{
			name:  "DNA matches - shared segments length",
			query: "CREATE INDEX IF NOT EXISTS idx_dna_matches_cm_desc ON dna_matches(total_shared_segments_length_in_cm DESC)",
		},
		{
			name:  "Individual surname - individual ID",
			query: "CREATE INDEX IF NOT EXISTS idx_individual_surname_individual_id ON individual_surname(individual_id)",
		},
		{
			name:  "Individual surname - surname ID",
			query: "CREATE INDEX IF NOT EXISTS idx_individual_surname_surname_id ON individual_surname(surname_id)",
		},
		{
			name:  "Individual place - individual ID",
			query: "CREATE INDEX IF NOT EXISTS idx_individual_place_individual_id ON individual_place(individual_id)",
		},
		{
			name:  "Individual place - place ID",
			query: "CREATE INDEX IF NOT EXISTS idx_individual_place_place_id ON individual_place(place_id)",
		},
		{
			name:  "Shared segment - match ID",
			query: "CREATE INDEX IF NOT EXISTS idx_shared_segment_match_id ON shared_segment(match_id)",
		},
		{
			name:  "Individuals - tree ID",
			query: "CREATE INDEX IF NOT EXISTS idx_individuals_tree_id ON individuals(tree_id)",
		},
		{
			name:  "Individual family - individual ID",
			query: "CREATE INDEX IF NOT EXISTS idx_individual_family_individual_id ON individual_family(individual_id)",
		},
	}

	fmt.Printf("📊 Applying %d critical database indexes...\n", len(indexes))
	fmt.Println("")

	for i, index := range indexes {
		fmt.Printf("[%d/%d] Creating index: %s", i+1, len(indexes), index.name)

		start := time.Now()
		err := db.Exec(index.query).Error
		duration := time.Since(start)

		if err != nil {
			fmt.Printf(" ❌ FAILED (%v)\n", err)
			return fmt.Errorf("failed to create index '%s': %v", index.name, err)
		}

		fmt.Printf(" ✅ SUCCESS (%.2fs)\n", duration.Seconds())
	}

	fmt.Println("")
	fmt.Println("📈 Updating table statistics...")

	// Update statistics for better query planning
	tables := []string{
		"dna_matches",
		"individual_surname",
		"individual_place",
		"shared_segment",
		"individuals",
		"individual_family",
		"surname",
		"place",
	}

	for _, table := range tables {
		fmt.Printf("   Analyzing table: %s", table)
		start := time.Now()
		err := db.Exec(fmt.Sprintf("ANALYZE TABLE %s", table)).Error
		duration := time.Since(start)

		if err != nil {
			fmt.Printf(" ⚠️  WARNING (%v)\n", err)
		} else {
			fmt.Printf(" ✅ (%.2fs)\n", duration.Seconds())
		}
	}

	return nil
}

func init() {
	// Set log flags for better output
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// Check if we're in the right directory
	if _, err := os.Stat("go.mod"); os.IsNotExist(err) {
		fmt.Println("❌ Error: Please run this from the project root directory")
		os.Exit(1)
	}
}
