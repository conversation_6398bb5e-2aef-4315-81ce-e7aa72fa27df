package pedigree

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"github.com/dmagur/myheritage/internal/fetch"
	log "github.com/sirupsen/logrus"
)

type HandlerInterface interface {
	Handle() error
}

type Handler struct {
	client     *fetch.MyHeritageApiClient
	logger     *log.Logger
	repository *Repository
}

func NewHandler(
	client *fetch.MyHeritageApiClient,
	logger *log.Logger,
	repository *Repository,
) Handler {
	return Handler{
		client:     client,
		logger:     logger,
		repository: repository,
	}
}

func (h Handler) Handle() error {

	dnaMatchIDs, err := h.repository.GetDnaMatchesWithoutPedigreeProcessed(0, 1500)

	if err != nil {
		return fmt.Errorf("error getting DNA matches from DB: %w", err)
	}

	offset := 0
	// Now loop through the slice of DnaMatch
	for _, match := range dnaMatchIDs {
		// Output the match details
		fmt.Println("-----------Pedigree--------------")
		fmt.Printf("Offset: %d\n", offset)
		fmt.Printf("Match ID: %s\n", match.ID)
		fmt.Printf("Match Individual ID: %s\n", match.MatchIndividualID)
		offset++

		apiResponse, err := h.Fetch(match.ID)

		if err != nil {
			return fmt.Errorf("error fetching DNA matches: %w", err)
		}

		err = h.repository.savePedigree(apiResponse.Data.DNAMatch.OtherDNAKit)

		if err != nil {
			fmt.Printf("Api response: %v\n", apiResponse.Data.DNAMatch.OtherDNAKit)
			return fmt.Errorf("error saving pedigree: %w", err)
		}

		randomNumber := rand.Intn(10) + 1
		time.Sleep(time.Duration(randomNumber) * time.Second)

		err = h.repository.setPedigreeProcessed(match.ID)
		if err != nil {
			return fmt.Errorf("error updating shared segments count: %w", err)
		}
	}

	return nil
}

func (h *Handler) Fetch(dnaMatchId string) (*ApiResponse, error) {
	url := "https://familygraphql.myheritage.com/dna_single_match_get_other_kit_pedigree_chart/"

	query := fmt.Sprintf(`"{dna_match(id:\"%s\",lang:\"DE\"){other_dna_kit{member{id name first_name last_name gender age_group personal_photo{thumbnails(thumbnail_size:\"96x96\"){url}}}associated_individual{...pedigree_chart}}}}fragment pedigree_chart on Individual{...individual_properties ...child_in_families close_family(hops:5,relationship_types:\"parent\",relationship_types_filter:\"biological\"){data{individual{...individual_properties ...child_in_families ...spouse_in_families}}}}fragment individual_properties on Individual{id name first_name last_name formatted_last_name gender is_alive is_privatized age_group personal_photo{thumbnails(thumbnail_size:\"96x96\"){url}}birth_date{...event_date_year}death_date{...event_date_year}}fragment child_in_families on Individual{child_in_families{child_type family{id}}}fragment spouse_in_families on Individual{spouse_in_families{id husband{id}wife{id}}}fragment event_date_year on EventDate{structured_date{first_date{year}}}"`,
		dnaMatchId)

	body, err := h.client.Fetch(url, query, "DNA Single Match - get other kit pedigree chart")
	if err != nil {
		fmt.Println("Error fetching response:", err)
		return nil, err
	}
	fmt.Printf("Response body: %s\n", string(body))

	var apiResponse ApiResponse
	err = json.Unmarshal(body, &apiResponse)
	if err != nil {
		// Print the response body if JSON unmarshalling fails
		fmt.Println("Error unmarshalling JSON response:", err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, err
	}

	return &apiResponse, nil
}
