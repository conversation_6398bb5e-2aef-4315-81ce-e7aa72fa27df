version: '3.8'

services:
  clique-scanner:
    build:
      context: .
      dockerfile: cmd/clique-scanner/Dockerfile
    environment:
      # Database configuration
      DB_TYPE: mariadb
      DB_HOST: *************
      DB_USER: myheritage
      DB_PASSWORD: Xq(RDl*5C7lkocf1
      DB_NAME: myheritage
      
      # Scanner configuration
      MIN_CM: ${MIN_CM:-50}
      MIN_SIZE: ${MIN_SIZE:-3}
      MAX_INDIVIDUALS: ${MAX_INDIVIDUALS:-1000}
      DRY_RUN: ${DRY_RUN:-false}
      VERBOSE: ${VERBOSE:-false}
    
    # Remove container after completion
    restart: "no"
    
    # Optional: mount logs directory
    volumes:
      - ./logs:/app/logs
    
    networks:
      - myheritage-network

networks:
  myheritage-network:
    external: true
