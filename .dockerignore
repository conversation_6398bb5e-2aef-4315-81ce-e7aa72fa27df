# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Binaries
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# IDE files
.idea/
.vscode/

# Logs
*.log

# Data directory
data/

# Backup files
*_backup/

# Visualizer web application (separate container)
visualizer-web/

# Analysis and documentation
analysis/
docs/
doc/

# 3D graph demo
3dgraph/

# Export data
export/

# Scripts
scripts/
