# DNA Clique Scanner

A background application that scans all individuals and DNA matches in the database to find cliques (groups where each individual has DNA matches with all other individuals in the group) and saves them to the database for fast retrieval.

## Overview

The DNA Clique Scanner uses the **<PERSON>ron-<PERSON><PERSON><PERSON>ch algorithm** to find all maximal cliques in the DNA match network. Instead of calculating cliques in real-time (which can be slow), this scanner pre-computes all cliques and stores them in the database, making the API responses much faster.

## Features

- **Bron-Kerbosch Algorithm**: Efficient maximal clique detection
- **Configurable Parameters**: Minimum cM threshold, clique size, processing limits
- **Database Storage**: Pre-computed results stored for fast API access
- **Progress Tracking**: Run status and statistics tracking
- **Dry Run Mode**: Test without saving to database
- **Verbose Logging**: Detailed progress information
- **Docker Support**: Containerized execution

## Usage

### Command Line

```bash
# Basic usage with defaults
go run cmd/clique-scanner/main.go cmd/clique-scanner/scanner.go

# With environment variables
MIN_CM=100 MIN_SIZE=4 MAX_INDIVIDUALS=2000 go run cmd/clique-scanner/main.go cmd/clique-scanner/scanner.go

# Dry run to see results without saving
DRY_RUN=true VERBOSE=true go run cmd/clique-scanner/main.go cmd/clique-scanner/scanner.go
```

### Using the Script

```bash
# Make script executable
chmod +x scripts/run-clique-scanner.sh

# Run with defaults
./scripts/run-clique-scanner.sh

# Run with custom parameters
./scripts/run-clique-scanner.sh --min-cm 100 --min-size 4 --max-individuals 2000

# Dry run with verbose output
./scripts/run-clique-scanner.sh --dry-run --verbose

# Show help
./scripts/run-clique-scanner.sh --help
```

### Using Docker

```bash
# Build and run with Docker Compose
docker-compose -f docker-compose.clique-scanner.yml up --build

# With custom environment variables
MIN_CM=75 MIN_SIZE=3 docker-compose -f docker-compose.clique-scanner.yml up --build

# Dry run
DRY_RUN=true VERBOSE=true docker-compose -f docker-compose.clique-scanner.yml up --build
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `MIN_CM` | 50 | Minimum cM threshold for DNA matches |
| `MIN_SIZE` | 3 | Minimum clique size to save |
| `MAX_INDIVIDUALS` | 1000 | Maximum individuals to process |
| `DRY_RUN` | false | Don't save to database, just show results |
| `VERBOSE` | false | Enable detailed logging |

### Database Configuration

The scanner uses the same database configuration as the main application:

| Variable | Description |
|----------|-------------|
| `DB_TYPE` | Database type (mariadb, mysql, postgres) |
| `DB_HOST` | Database host |
| `DB_USER` | Database username |
| `DB_PASSWORD` | Database password |
| `DB_NAME` | Database name |

## Database Schema

The scanner creates two tables:

### `dna_cliques`
Stores pre-computed cliques:
- `id` - Primary key
- `size` - Number of individuals in clique
- `min_cm` - Minimum cM value used for computation
- `avg_cm` - Average cM value within clique
- `max_cm` - Maximum cM value within clique
- `total_connections` - Total connections in clique
- `members` - JSON array of individual IDs
- `created_at` / `updated_at` - Timestamps

### `dna_clique_runs`
Tracks scanning runs:
- `id` - Primary key
- `status` - running, completed, failed
- `min_cm` - Parameters used
- `min_size` - Parameters used
- `max_individuals` - Parameters used
- `total_individuals` - Number processed
- `total_cliques` - Number found
- `processing_time_ms` - Execution time
- `error_message` - Error details if failed
- `started_at` / `completed_at` - Timestamps

## Algorithm

The scanner uses the **Bron-Kerbosch algorithm** for finding maximal cliques:

1. **Get Connected Individuals**: Find all individuals with DNA matches ≥ min_cm
2. **Build Adjacency Map**: Create graph of connections between individuals
3. **Find Cliques**: Use Bron-Kerbosch to find all maximal cliques
4. **Filter & Process**: Keep cliques ≥ min_size, calculate statistics
5. **Save to Database**: Store results for fast API access

## Performance

- **Memory Usage**: Scales with number of individuals and connections
- **Processing Time**: Depends on network density and parameters
- **Database Impact**: Clears and rebuilds clique tables on each run
- **Recommended Limits**: Start with 1000 individuals, increase as needed

## Integration

The API server automatically uses pre-computed cliques from the database:

- **Endpoint**: `/api/v1/analytics/network/cliques`
- **Fast Response**: No real-time computation needed
- **Filtering**: API filters pre-computed results by parameters
- **Fallback**: Graceful handling if no pre-computed data available

## Scheduling

For production use, consider scheduling regular runs:

```bash
# Crontab example - run daily at 2 AM
0 2 * * * /path/to/scripts/run-clique-scanner.sh --min-cm 50 --min-size 3

# Or use a more sophisticated scheduler like systemd timers
```

## Monitoring

Check run status and results:

```sql
-- View recent runs
SELECT * FROM dna_clique_runs ORDER BY created_at DESC LIMIT 10;

-- View clique statistics
SELECT size, COUNT(*) as count, AVG(avg_cm) as avg_cm 
FROM dna_cliques 
GROUP BY size 
ORDER BY size;

-- View largest cliques
SELECT size, avg_cm, members 
FROM dna_cliques 
ORDER BY size DESC, avg_cm DESC 
LIMIT 10;
```
