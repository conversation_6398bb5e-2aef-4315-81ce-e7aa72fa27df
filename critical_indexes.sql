-- Critical Database Indexes for Immediate Performance Improvement
-- Focus on the most impactful indexes first

-- 1. DNA MATCHES - Most critical (50+ second queries)
CREATE INDEX IF NOT EXISTS idx_dna_matches_source_individual 
ON dna_matches(source_individual_id);

CREATE INDEX IF NOT EXISTS idx_dna_matches_match_individual 
ON dna_matches(match_individual_id);

-- 2. INDIVIDUAL_SURNAME - Second most critical (20+ second queries)
CREATE INDEX IF NOT EXISTS idx_individual_surname_individual_id 
ON individual_surname(individual_id);

CREATE INDEX IF NOT EXISTS idx_individual_surname_surname_id 
ON individual_surname(surname_id);

-- 3. INDIVIDUAL_PLACE - Third most critical (slow place queries)
CREATE INDEX IF NOT EXISTS idx_individual_place_individual_id 
ON individual_place(individual_id);

CREATE INDEX IF NOT EXISTS idx_individual_place_place_id 
ON individual_place(place_id);

-- 4. SHARED_SEGMENT - Fourth most critical
CREATE INDEX IF NOT EXISTS idx_shared_segment_match_id 
ON shared_segment(match_id);

-- Update statistics
ANALYZE TABLE dna_matches;
ANALYZE TABLE individual_surname;
ANALYZE TABLE individual_place;
ANALYZE TABLE shared_segment;
