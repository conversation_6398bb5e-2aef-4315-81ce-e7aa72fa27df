FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o clique-scanner cmd/clique-scanner/main.go cmd/clique-scanner/scanner.go

# Create a minimal image
FROM alpine:latest

WORKDIR /app

# Install tzdata for timezone support
RUN apk add --no-cache tzdata

# Copy the binary from the builder stage
COPY --from=builder /app/clique-scanner .

# Run the application
CMD ["./clique-scanner"]
