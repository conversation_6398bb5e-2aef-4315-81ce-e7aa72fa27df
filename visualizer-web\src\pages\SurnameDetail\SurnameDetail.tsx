import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  AccountTree as TreeIcon,
  Refresh as RefreshIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { getSurnameDetails, SurnameDetailsResponse } from '../../services/api';

const SurnameDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: surnameData, isLoading, error, refetch } = useQuery<SurnameDetailsResponse>({
    queryKey: ['surname-details', id],
    queryFn: () => getSurnameDetails(id!),
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">
          Failed to load surname details. Please try again.
        </Alert>
      </Box>
    );
  }

  if (!surnameData) {
    return (
      <Box p={3}>
        <Alert severity="warning">
          Surname not found.
        </Alert>
      </Box>
    );
  }

  const { surname, individuals, places, trees, stats } = surnameData;

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="between" mb={3}>
        <Box display="flex" alignItems="center" gap={2}>
          <IconButton onClick={() => navigate(-1)}>
            <ArrowBackIcon />
          </IconButton>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
              {surname.Surname}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Surname ID: {surname.ID}
            </Typography>
          </Box>
        </Box>
        <Tooltip title="Refresh data">
          <IconButton onClick={() => refetch()}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <PersonIcon color="primary" />
                <Box>
                  <Typography variant="h6">{stats.total_individuals}</Typography>
                  <Typography variant="body2" color="text.secondary">Individuals</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <LocationIcon color="primary" />
                <Box>
                  <Typography variant="h6">{stats.total_places}</Typography>
                  <Typography variant="body2" color="text.secondary">Places</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <TreeIcon color="primary" />
                <Box>
                  <Typography variant="h6">{stats.total_trees}</Typography>
                  <Typography variant="body2" color="text.secondary">Family Trees</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <PeopleIcon color="primary" />
                <Box>
                  <Typography variant="h6">
                    {Object.values(stats.gender_distribution || {}).reduce((a: number, b: number) => a + b, 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">Total People</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Demographics */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Demographics</Typography>

            <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>Gender Distribution</Typography>
            <Box display="flex" gap={1} flexWrap="wrap" mb={2}>
              {Object.entries(stats.gender_distribution || {}).map(([gender, count]) => (
                <Chip
                  key={gender}
                  label={`${gender}: ${count}`}
                  variant="outlined"
                  size="small"
                />
              ))}
            </Box>

            <Typography variant="subtitle2" gutterBottom>Age Group Distribution</Typography>
            <Box display="flex" gap={1} flexWrap="wrap">
              {Object.entries(stats.age_group_distribution || {}).map(([ageGroup, count]) => (
                <Chip
                  key={ageGroup}
                  label={`${ageGroup}: ${count}`}
                  variant="outlined"
                  size="small"
                />
              ))}
            </Box>
          </Paper>
        </Grid>

        {/* Places */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Associated Places ({places.length})</Typography>
            <List sx={{ maxHeight: 300, overflow: 'auto' }}>
              {places.slice(0, 20).map((place) => (
                <ListItem key={place.ID} disablePadding>
                  <ListItemIcon>
                    <LocationIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${place.Country || 'Unknown Country'}${place.StateOrProvince ? `, ${place.StateOrProvince}` : ''}`}
                    secondary={`${place.CountryCode || ''}${place.StateOrProvinceCode ? `, ${place.StateOrProvinceCode}` : ''}`}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Family Trees */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Family Trees ({trees.length})</Typography>
            <TableContainer sx={{ maxHeight: 400 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>Tree Name</TableCell>
                    <TableCell>Individual Count</TableCell>
                    <TableCell>Creator</TableCell>
                    <TableCell>Creator Country</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {trees.slice(0, 50).map((tree) => (
                    <TableRow key={tree.ID} hover>
                      <TableCell>{tree.Name}</TableCell>
                      <TableCell>{tree.IndividualCount?.toLocaleString() || 'Unknown'}</TableCell>
                      <TableCell>{tree.SiteCreatorName || 'Unknown'}</TableCell>
                      <TableCell>{tree.SiteCreatorCountry || 'Unknown'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* Individuals */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Individuals with this Surname ({individuals.length})</Typography>
            <TableContainer sx={{ maxHeight: 400 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Birth Year</TableCell>
                    <TableCell>Gender</TableCell>
                    <TableCell>Age Group</TableCell>
                    <TableCell>Birth Place</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {individuals.slice(0, 100).map((individual) => (
                    <TableRow
                      key={individual.ID}
                      hover
                      sx={{ cursor: 'pointer' }}
                      onClick={() => navigate(`/individuals/${individual.ID}`)}
                    >
                      <TableCell>{individual.Name || 'Unknown'}</TableCell>
                      <TableCell>{individual.BirthDateYear || 'Unknown'}</TableCell>
                      <TableCell>{individual.Gender || 'Unknown'}</TableCell>
                      <TableCell>{individual.AgeGroupInYears || 'Unknown'}</TableCell>
                      <TableCell>{individual.BirthPlace || 'Unknown'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SurnameDetail;
