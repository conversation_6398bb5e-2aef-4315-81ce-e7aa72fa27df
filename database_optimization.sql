-- Database Performance Optimization Script
-- This script adds strategic indexes to improve query performance
-- Based on analysis of slow queries from API server logs

-- =============================================================================
-- CRITICAL INDEXES FOR IMMEDIATE PERFORMANCE IMPROVEMENT
-- =============================================================================

-- 1. DNA MATCHES TABLE OPTIMIZATION
-- These queries are taking 50+ seconds, need immediate optimization

-- Index for source_individual_id and match_individual_id lookups
CREATE INDEX IF NOT EXISTS idx_dna_matches_source_individual 
ON dna_matches(source_individual_id);

CREATE INDEX IF NOT EXISTS idx_dna_matches_match_individual 
ON dna_matches(match_individual_id);

-- Composite index for the most common query pattern
CREATE INDEX IF NOT EXISTS idx_dna_matches_individuals_cm 
ON dna_matches(source_individual_id, match_individual_id, total_shared_segments_length_in_cm DESC);

-- Index for ordering by shared segments length
CREATE INDEX IF NOT EXISTS idx_dna_matches_cm_desc 
ON dna_matches(total_shared_segments_length_in_cm DESC);

-- 2. INDIVIDUAL_SURNAME TABLE OPTIMIZATION
-- Surname popularity queries are taking 20+ seconds

-- Index for individual_id lookups
CREATE INDEX IF NOT EXISTS idx_individual_surname_individual_id 
ON individual_surname(individual_id);

-- Index for surname_id lookups (popularity counting)
CREATE INDEX IF NOT EXISTS idx_individual_surname_surname_id 
ON individual_surname(surname_id);

-- Composite index for join operations
CREATE INDEX IF NOT EXISTS idx_individual_surname_composite 
ON individual_surname(surname_id, individual_id);

-- 3. INDIVIDUAL_PLACE TABLE OPTIMIZATION
-- Place popularity queries are slow

-- Index for individual_id lookups
CREATE INDEX IF NOT EXISTS idx_individual_place_individual_id 
ON individual_place(individual_id);

-- Index for place_id lookups (popularity counting)
CREATE INDEX IF NOT EXISTS idx_individual_place_place_id 
ON individual_place(place_id);

-- Composite index for join operations
CREATE INDEX IF NOT EXISTS idx_individual_place_composite 
ON individual_place(place_id, individual_id);

-- 4. SHARED_SEGMENT TABLE OPTIMIZATION
-- Shared segment queries are slow

-- Index for match_id lookups
CREATE INDEX IF NOT EXISTS idx_shared_segment_match_id 
ON shared_segment(match_id);

-- Index for chromosome and position queries
CREATE INDEX IF NOT EXISTS idx_shared_segment_chromosome 
ON shared_segment(chromosome_id, start_position);

-- 5. INDIVIDUALS TABLE OPTIMIZATION
-- Individual lookups and filtering

-- Index for tree_id (used in joins)
CREATE INDEX IF NOT EXISTS idx_individuals_tree_id 
ON individuals(tree_id);

-- Index for gender filtering
CREATE INDEX IF NOT EXISTS idx_individuals_gender 
ON individuals(gender);

-- Index for age group filtering
CREATE INDEX IF NOT EXISTS idx_individuals_age_group 
ON individuals(age_group);

-- 6. TREES TABLE OPTIMIZATION
-- Tree-related queries

-- Index for creator country filtering
CREATE INDEX IF NOT EXISTS idx_trees_creator_country 
ON trees(site_creator_country);

-- Index for individual count ordering
CREATE INDEX IF NOT EXISTS idx_trees_individual_count 
ON trees(individual_count DESC);

-- 7. INDIVIDUAL_FAMILY TABLE OPTIMIZATION
-- Family relationship queries

-- Index for individual_id lookups
CREATE INDEX IF NOT EXISTS idx_individual_family_individual_id 
ON individual_family(individual_id);

-- Index for family_id lookups
CREATE INDEX IF NOT EXISTS idx_individual_family_family_id 
ON individual_family(family_id);

-- =============================================================================
-- ADDITIONAL PERFORMANCE INDEXES
-- =============================================================================

-- 8. PLACE TABLE OPTIMIZATION
-- Geographic filtering and searching

-- Index for country lookups
CREATE INDEX IF NOT EXISTS idx_place_country 
ON place(country);

-- Index for country code lookups
CREATE INDEX IF NOT EXISTS idx_place_country_code 
ON place(country_code);

-- 9. SURNAME TABLE OPTIMIZATION
-- Surname searching and filtering

-- Index for surname lookups (case-insensitive)
CREATE INDEX IF NOT EXISTS idx_surname_surname 
ON surname(surname);

-- 10. API_TOKENS TABLE OPTIMIZATION
-- Token validation queries

-- Index for active token lookups (already exists but ensuring)
CREATE INDEX IF NOT EXISTS idx_api_tokens_is_active 
ON api_tokens(is_active);

-- =============================================================================
-- QUERY-SPECIFIC COMPOSITE INDEXES
-- =============================================================================

-- 11. For the complex surname popularity query
-- SELECT s.*, (SELECT COUNT(*) FROM individual_surname WHERE surname_id = s.id) as popularity_count
-- FROM surname s JOIN individual_surname is_user ON s.id = is_user.surname_id
-- WHERE is_user.individual_id = ? ORDER BY popularity_count DESC, s.surname ASC

CREATE INDEX IF NOT EXISTS idx_surname_popularity_optimization 
ON individual_surname(individual_id, surname_id);

-- 12. For the complex place popularity query
-- SELECT p.*, (SELECT COUNT(*) FROM individual_place WHERE place_id = p.id) as popularity_count
-- FROM place p JOIN individual_place ip_user ON p.id = ip_user.place_id
-- WHERE ip_user.individual_id = ? ORDER BY popularity_count DESC

CREATE INDEX IF NOT EXISTS idx_place_popularity_optimization 
ON individual_place(individual_id, place_id);

-- 13. For DNA matches with segments query
-- Complex query involving dna_matches and shared_segment tables

CREATE INDEX IF NOT EXISTS idx_dna_matches_segments_optimization 
ON dna_matches(source_individual_id, match_individual_id, id);

-- =============================================================================
-- STATISTICS UPDATE
-- =============================================================================

-- Update table statistics for better query planning
-- (MariaDB specific - adjust for PostgreSQL if needed)
ANALYZE TABLE dna_matches;
ANALYZE TABLE individuals;
ANALYZE TABLE individual_surname;
ANALYZE TABLE individual_place;
ANALYZE TABLE shared_segment;
ANALYZE TABLE surname;
ANALYZE TABLE place;
ANALYZE TABLE trees;
ANALYZE TABLE individual_family;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Check index usage and effectiveness
-- Run these queries after creating indexes to verify improvement

-- 1. Test DNA matches query performance
-- EXPLAIN SELECT * FROM dna_matches 
-- WHERE source_individual_id = 'individual-1001098971-1500001' 
-- OR match_individual_id = 'individual-1001098971-1500001' 
-- ORDER BY total_shared_segments_length_in_cm DESC LIMIT 100;

-- 2. Test surname popularity query performance
-- EXPLAIN SELECT s.*, (SELECT COUNT(*) FROM individual_surname WHERE surname_id = s.id) as popularity_count
-- FROM surname s JOIN individual_surname is_user ON s.id = is_user.surname_id
-- WHERE is_user.individual_id = 'individual-1001098971-1500001'
-- ORDER BY popularity_count DESC, s.surname ASC;

-- 3. Test place popularity query performance
-- EXPLAIN SELECT p.*, (SELECT COUNT(*) FROM individual_place WHERE place_id = p.id) as popularity_count
-- FROM place p JOIN individual_place ip_user ON p.id = ip_user.place_id
-- WHERE ip_user.individual_id = 'individual-1001098971-1500001'
-- ORDER BY popularity_count DESC, p.country ASC, p.state_or_province ASC;

-- =============================================================================
-- EXPECTED PERFORMANCE IMPROVEMENTS
-- =============================================================================

-- Before optimization:
-- - DNA matches query: 50+ seconds
-- - Surname popularity query: 20+ seconds  
-- - Place popularity query: 2+ seconds
-- - Individual details API: 1+ minutes (timeout)

-- After optimization (expected):
-- - DNA matches query: 1-3 seconds
-- - Surname popularity query: 1-2 seconds
-- - Place popularity query: 0.5-1 seconds
-- - Individual details API: 5-10 seconds

-- =============================================================================
-- MAINTENANCE NOTES
-- =============================================================================

-- 1. Monitor index usage with:
--    SHOW INDEX FROM table_name;
--    
-- 2. Check query performance with:
--    EXPLAIN ANALYZE SELECT ...;
--    
-- 3. Update statistics regularly:
--    ANALYZE TABLE table_name;
--    
-- 4. Consider removing unused indexes if they impact write performance
--
-- 5. Monitor disk space usage as indexes require additional storage
