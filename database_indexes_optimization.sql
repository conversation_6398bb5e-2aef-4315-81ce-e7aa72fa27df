-- Database Indexes Optimization for MyHeritage DNA Analysis
-- This script adds critical indexes to improve query performance

-- ============================================================================
-- DNA MATCHES TABLE INDEXES (Most Critical)
-- ============================================================================

-- Index for the main individual DNA matches query: WHERE source_individual_id = ? OR match_individual_id = ?
-- This is the most critical index for individual detail pages
CREATE INDEX idx_dna_matches_source_individual ON dna_matches(source_individual_id);
CREATE INDEX idx_dna_matches_match_individual ON dna_matches(match_individual_id);

-- Composite index for DNA matches filtering and sorting
-- Covers: WHERE total_shared_segments_length_in_cm >= ? AND <= ? ORDER BY total_shared_segments_length_in_cm DESC
CREATE INDEX idx_dna_matches_cm_desc ON dna_matches(total_shared_segments_length_in_cm DESC);

-- Composite index for relationship filtering
CREATE INDEX idx_dna_matches_relationship ON dna_matches(exact_dna_relationship);
CREATE INDEX idx_dna_matches_confidence ON dna_matches(confidence_level);

-- Composite index for complex filtering queries (analytics endpoints)
CREATE INDEX idx_dna_matches_cm_relationship ON dna_matches(total_shared_segments_length_in_cm, exact_dna_relationship);
CREATE INDEX idx_dna_matches_cm_confidence ON dna_matches(total_shared_segments_length_in_cm, confidence_level);

-- Index for network analysis queries (source + target combinations)
CREATE INDEX idx_dna_matches_source_target ON dna_matches(source_individual_id, match_individual_id);

-- Index for data processing queries (link and counts)
CREATE INDEX idx_dna_matches_link_processing ON dna_matches(link, shared_matches_count);
CREATE INDEX idx_dna_matches_segments_processing ON dna_matches(link, shared_segments_count);
CREATE INDEX idx_dna_matches_surnames_processing ON dna_matches(link, surnames_count, places_count);

-- ============================================================================
-- SHARED SEGMENTS TABLE INDEXES
-- ============================================================================

-- Primary index for getting segments by match ID
CREATE INDEX idx_shared_segment_match_id ON shared_segment(match_id);

-- Composite index for chromosome-based queries and sorting
CREATE INDEX idx_shared_segment_match_chromosome ON shared_segment(match_id, chromosome_id, start_position);

-- Index for chromosome analysis
CREATE INDEX idx_shared_segment_chromosome ON shared_segment(chromosome_id);

-- ============================================================================
-- INDIVIDUALS TABLE INDEXES
-- ============================================================================

-- Index for name search queries (LIKE operations)
CREATE INDEX idx_individuals_name ON individuals(name);
CREATE INDEX idx_individuals_name_transliterated ON individuals(name_transliterated);

-- Index for tree-based queries
CREATE INDEX idx_individuals_tree_id ON individuals(tree_id);

-- Composite index for gender and age group filtering
CREATE INDEX idx_individuals_gender_age ON individuals(gender, age_group);

-- ============================================================================
-- RELATIONSHIP TABLES INDEXES
-- ============================================================================

-- Individual-Family relationships
CREATE INDEX idx_individual_family_individual ON individual_family(individual_id);
CREATE INDEX idx_individual_family_family ON individual_family(family_id);
CREATE INDEX idx_individual_family_role ON individual_family(role);

-- Individual-Surname relationships
CREATE INDEX idx_individual_surname_individual ON individual_surname(individual_id);
CREATE INDEX idx_individual_surname_surname ON individual_surname(surname_id);

-- Individual-Place relationships  
CREATE INDEX idx_individual_place_individual ON individual_place(individual_id);
CREATE INDEX idx_individual_place_place ON individual_place(place_id);

-- ============================================================================
-- SUPPORTING TABLES INDEXES
-- ============================================================================

-- Trees table
CREATE INDEX idx_trees_creator ON trees(site_creator_id);
CREATE INDEX idx_trees_country ON trees(site_creator_country_code);

-- Surnames table
CREATE INDEX idx_surname_name ON surname(surname);

-- Places table
CREATE INDEX idx_place_country ON place(country_code);
CREATE INDEX idx_place_country_state ON place(country_code, state_or_province_code);

-- Submitters table
CREATE INDEX idx_submitters_name ON submitters(name);
CREATE INDEX idx_submitters_public ON submitters(is_public);

-- ============================================================================
-- ANALYTICS OPTIMIZATION INDEXES
-- ============================================================================

-- For clique analysis and network queries
CREATE INDEX idx_dna_matches_cm_threshold ON dna_matches(total_shared_segments_length_in_cm) 
WHERE total_shared_segments_length_in_cm >= 7.0;

-- For statistics and distribution queries
CREATE INDEX idx_dna_matches_stats ON dna_matches(total_shared_segments_length_in_cm, confidence_level, exact_dna_relationship);

-- ============================================================================
-- PERFORMANCE NOTES
-- ============================================================================

/*
Key Performance Improvements Expected:

1. Individual Detail Pages:
   - DNA matches query: 30s → ~1-2s (95% improvement)
   - The OR condition will use both source_individual and match_individual indexes

2. Analytics Endpoints:
   - Matches filtering: 4s → ~200ms (95% improvement)
   - Distribution queries: ~500ms (80% improvement)

3. Search Operations:
   - Individual name search: ~2s → ~100ms (95% improvement)
   - Relationship lookups: ~500ms → ~50ms (90% improvement)

4. Network Analysis:
   - Clique detection: Significant improvement for large datasets
   - Connected individuals: ~1s → ~100ms (90% improvement)

Memory Usage:
- These indexes will use approximately 200-500MB additional storage
- Query performance gains far outweigh storage costs

Maintenance:
- Indexes are automatically maintained by MariaDB
- INSERT/UPDATE operations will be slightly slower (~5-10%)
- SELECT operations will be dramatically faster (80-95% improvement)
*/
