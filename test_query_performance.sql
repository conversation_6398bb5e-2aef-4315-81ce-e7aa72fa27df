-- Test query performance and execution plan

-- Check current indexes on dna_matches table
SHOW INDEX FROM dna_matches;

-- Explain the problematic query
EXPLAIN SELECT * FROM dna_matches 
WHERE source_individual_id = 'individual-32361932-1000006' 
   OR match_individual_id = 'individual-32361932-1000006' 
ORDER BY total_shared_segments_length_in_cm DESC 
LIMIT 100;

-- Test individual parts of the query
EXPLAIN SELECT * FROM dna_matches 
WHERE source_individual_id = 'individual-32361932-1000006' 
ORDER BY total_shared_segments_length_in_cm DESC 
LIMIT 100;

EXPLAIN SELECT * FROM dna_matches 
WHERE match_individual_id = 'individual-32361932-1000006' 
ORDER BY total_shared_segments_length_in_cm DESC 
LIMIT 100;

-- Count matches for this individual
SELECT COUNT(*) as source_matches FROM dna_matches WHERE source_individual_id = 'individual-32361932-1000006';
SELECT COUNT(*) as match_matches FROM dna_matches WHERE match_individual_id = 'individual-32361932-1000006';

-- Test performance of individual queries
SELECT COUNT(*) FROM dna_matches WHERE source_individual_id = 'individual-32361932-1000006';
SELECT COUNT(*) FROM dna_matches WHERE match_individual_id = 'individual-32361932-1000006';
