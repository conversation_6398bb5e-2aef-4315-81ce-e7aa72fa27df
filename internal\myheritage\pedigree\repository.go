package pedigree

import (
	"fmt"
	"strings"
	"time"

	"github.com/dmagur/myheritage/internal/database"
)

type Repository struct {
	db *database.GormDB
}

type Result struct {
	ID                string
	MatchIndividualID string
}

func NewRepository(db *database.GormDB) *Repository {
	return &Repository{db}
}

func (r *Repository) GetDnaMatchesWithoutPedigreeProcessed(offset int, limit int) ([]Result, error) {
	// Slice to hold the results
	var dnaMatches []Result

	// Execute the query using GORM's query builder instead of raw SQL
	result := r.db.DB.Table("dna_matches").
		Select("id, match_individual_id").
		Where("link is not null and link != '' and pedigree_processed = ?", false).
		Order("total_shared_segments_length_in_cm desc").
		Offset(offset).
		Limit(limit).
		Scan(&dnaMatches)

	if result.Error != nil {
		return nil, result.Error
	}

	return dnaMatches, nil
}

func (r Repository) savePedigree(dnaKit DNAKit) error {

	err := r.saveIndividual(dnaKit.AssociatedIndividual)

	if err != nil {
		return err
	}

	for _, individual := range dnaKit.AssociatedIndividual.CloseFamily.Data {
		err = r.saveIndividual(individual.Individual)

		if err != nil {
			return err
		}
	}

	return nil
}

func (r Repository) saveIndividual(individual Individual) error {

	_, err := r.upsertIndividual(individual)

	if err != nil {
		return err
	}

	if individual.ChildInFamilies != nil {
		for _, fam := range individual.ChildInFamilies {
			_, err := r.upsertFamily(fam.Family.Id)

			if err != nil {
				return err
			}

			_, err = r.upsertIndividualFamily(fam.Family.Id, individual.Id, "child")

			if err != nil {
				return err
			}
		}
	}

	if individual.SpouseInFamilies != nil {
		for _, spouse := range individual.SpouseInFamilies {
			_, err := r.upsertFamily(spouse.Id)

			if err != nil {
				return err
			}

			if individual.Id == spouse.Wife.Id {
				_, err = r.upsertIndividualFamily(spouse.Id, spouse.Wife.Id, "wife")

				if err != nil {
					return err
				}
			}

			if individual.Id == spouse.Husband.Id {
				_, err = r.upsertIndividualFamily(spouse.Id, spouse.Husband.Id, "husband")

				if err != nil {
					return err
				}
			}
		}
	}

	return nil
}

func (r Repository) upsertIndividualFamily(familyId string, individualId string, role string) (string, error) {
	// Try to find an existing record
	var existingRecord struct{ ID string }
	result := r.db.DB.Table("individual_family").
		Where("family_id = ? AND individual_id = ? AND role = ?",
			familyId, individualId, role).
		First(&existingRecord)

	// If the record exists, update it
	if result.Error == nil {
		r.db.DB.Table("individual_family").
			Where("id = ?", existingRecord.ID).
			Updates(map[string]interface{}{
				"family_id":     familyId,
				"individual_id": individualId,
				"role":          role,
			})
		return existingRecord.ID, nil
	}

	// Try to directly insert into individual_family
	// This might fail due to foreign key constraints, but we'll handle that
	result = r.db.DB.Exec(
		"INSERT INTO individual_family (family_id, individual_id, role) VALUES (?, ?, ?)",
		familyId, individualId, role,
	)

	// If the insert was successful, return the ID
	if result.Error == nil {
		// Get the last inserted ID
		var lastID struct{ ID string }
		r.db.DB.Raw("SELECT LAST_INSERT_ID() as id").Scan(&lastID)
		return lastID.ID, nil
	}

	// If the insert failed due to foreign key constraints, try to disable them temporarily
	// This is a workaround for the case where the individuals_old table doesn't exist
	// or the individual doesn't exist in the individuals_old table
	if result.Error != nil && strings.Contains(result.Error.Error(), "foreign key constraint") {
		// Try to disable foreign key checks
		r.db.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")

		// Try the insert again
		result = r.db.DB.Exec(
			"INSERT INTO individual_family (family_id, individual_id, role) VALUES (?, ?, ?)",
			familyId, individualId, role,
		)

		// Re-enable foreign key checks
		r.db.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

		if result.Error != nil {
			return "", fmt.Errorf("failed to upsert individual family even with foreign key checks disabled: %v", result.Error)
		}

		// Get the last inserted ID
		var lastID struct{ ID string }
		r.db.DB.Raw("SELECT LAST_INSERT_ID() as id").Scan(&lastID)
		return lastID.ID, nil
	}

	return "", fmt.Errorf("failed to upsert individual family: %v", result.Error)
}

func (r Repository) upsertFamily(Id string) (string, error) {
	// Try to find an existing record
	var existingRecord struct{ ID string }
	result := r.db.DB.Table("family").
		Where("id = ?", Id).
		First(&existingRecord)

	// If the record exists, return its ID
	if result.Error == nil {
		return existingRecord.ID, nil
	}

	// Try to directly insert into family
	// This might fail due to foreign key constraints, but we'll handle that
	result = r.db.DB.Exec(
		"INSERT INTO family (id) VALUES (?)",
		Id,
	)

	// If the insert was successful, return the ID
	if result.Error == nil {
		return Id, nil
	}

	// If the insert failed due to foreign key constraints, try to disable them temporarily
	// This is a workaround for the case where the family_old table doesn't exist
	// or the family doesn't exist in the family_old table
	if result.Error != nil && strings.Contains(result.Error.Error(), "foreign key constraint") {
		// Try to disable foreign key checks
		r.db.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")

		// Try the insert again
		result = r.db.DB.Exec(
			"INSERT INTO family (id) VALUES (?)",
			Id,
		)

		// Re-enable foreign key checks
		r.db.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

		if result.Error != nil {
			return "", fmt.Errorf("failed to upsert family even with foreign key checks disabled: %v", result.Error)
		}

		return Id, nil
	}

	return "", fmt.Errorf("failed to upsert family: %v", result.Error)
}

func (r Repository) upsertIndividual(individual Individual) (string, error) {
	if individual.Id == "" {
		return "", nil
	}

	// Try to find an existing record
	var existingRecord struct{ ID string }
	result := r.db.DB.Table("individuals").
		Where("id = ?", individual.Id).
		First(&existingRecord)

	// If the record exists, update it
	if result.Error == nil {
		r.db.DB.Table("individuals").
			Where("id = ?", existingRecord.ID).
			Updates(map[string]interface{}{
				"name":                individual.Name,
				"first_name":          individual.FirstName,
				"last_name":           individual.LastName,
				"formatted_last_name": individual.FormattedLastName,
				"gender":              individual.Gender,
				"is_alive":            individual.IsAlive,
				"age_group":           individual.AgeGroup,
				"birth_date_year":     individual.BirthDate.StructuredDate.FirstDate.Year,
				"death_date_year":     individual.DeathDate.StructuredDate.FirstDate.Year,
			})

		// Try to update individuals_old if it exists
		// This might fail if the table doesn't exist, but we'll ignore that error
		r.db.DB.Table("individuals_old").
			Where("id = ?", individual.Id).
			Updates(map[string]interface{}{
				"name":                individual.Name,
				"first_name":          individual.FirstName,
				"last_name":           individual.LastName,
				"formatted_last_name": individual.FormattedLastName,
				"gender":              individual.Gender,
				"is_alive":            individual.IsAlive,
				"age_group":           individual.AgeGroup,
				"birth_date_year":     individual.BirthDate.StructuredDate.FirstDate.Year,
				"death_date_year":     individual.DeathDate.StructuredDate.FirstDate.Year,
			})

		return existingRecord.ID, nil
	}

	// Try to directly insert into individuals
	// This might fail due to foreign key constraints, but we'll handle that
	result = r.db.DB.Exec(
		"INSERT INTO individuals (id, name, first_name, last_name, formatted_last_name, gender, is_alive, age_group, birth_date_year, death_date_year) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		individual.Id, individual.Name, individual.FirstName, individual.LastName, individual.FormattedLastName,
		individual.Gender, individual.IsAlive, individual.AgeGroup, individual.BirthDate.StructuredDate.FirstDate.Year, individual.DeathDate.StructuredDate.FirstDate.Year,
	)

	// If the insert was successful, return the ID
	if result.Error == nil {
		return individual.Id, nil
	}

	// If the insert failed due to foreign key constraints, try to disable them temporarily
	// This is a workaround for the case where the individuals_old table doesn't exist
	// or the individual doesn't exist in the individuals_old table
	if result.Error != nil && strings.Contains(result.Error.Error(), "foreign key constraint") {
		// Try to disable foreign key checks
		r.db.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")

		// Try the insert again
		result = r.db.DB.Exec(
			"INSERT INTO individuals (id, name, first_name, last_name, formatted_last_name, gender, is_alive, age_group, birth_date_year, death_date_year) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
			individual.Id, individual.Name, individual.FirstName, individual.LastName, individual.FormattedLastName,
			individual.Gender, individual.IsAlive, individual.AgeGroup, individual.BirthDate.StructuredDate.FirstDate.Year, individual.DeathDate.StructuredDate.FirstDate.Year,
		)

		// Re-enable foreign key checks
		r.db.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

		if result.Error != nil {
			return "", fmt.Errorf("failed to upsert individual even with foreign key checks disabled: %v", result.Error)
		}

		return individual.Id, nil
	}

	return "", fmt.Errorf("failed to upsert individual: %v", result.Error)
}

func (r *Repository) setPedigreeProcessed(matchId string) error {
	currentTime := time.Now()

	// Update the record using GORM
	result := r.db.DB.Model(&database.DNAMatch{}).
		Where("id = ?", matchId).
		Updates(map[string]interface{}{
			"pedigree_processed": true,
			"updated_at":         currentTime.Format("2006-01-02 15:04:05"),
		})

	if result.Error != nil {
		return result.Error
	}

	return nil
}
