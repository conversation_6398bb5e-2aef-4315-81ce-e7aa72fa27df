-- Test individual queries to see if they work

-- Test source matches query
SELECT COUNT(*) as source_count FROM dna_matches 
WHERE source_individual_id = 'individual-32361932-1000006' 
ORDER BY total_shared_segments_length_in_cm DESC 
LIMIT 50;

-- Test match matches query  
SELECT COUNT(*) as match_count FROM dna_matches 
WHERE match_individual_id = 'individual-32361932-1000006' 
ORDER BY total_shared_segments_length_in_cm DESC 
LIMIT 50;

-- Test actual data retrieval
SELECT source_individual_id, match_individual_id, total_shared_segments_length_in_cm 
FROM dna_matches 
WHERE source_individual_id = 'individual-32361932-1000006' 
ORDER BY total_shared_segments_length_in_cm DESC 
LIMIT 5;

SELECT source_individual_id, match_individual_id, total_shared_segments_length_in_cm 
FROM dna_matches 
WHERE match_individual_id = 'individual-32361932-1000006' 
ORDER BY total_shared_segments_length_in_cm DESC 
LIMIT 5;
