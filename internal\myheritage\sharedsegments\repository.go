package sharedsegments

import (
	"fmt"
	"time"

	"github.com/dmagur/myheritage/internal/database"
)

type Repository struct {
	db *database.GormDB
}

type Result struct {
	ID                string
	MatchIndividualID string
}

func NewRepository(db *database.GormDB) *Repository {
	return &Repository{db}
}

func (r *Repository) GetDnaMatchesWithoutSharedSegments(offset int, limit int) ([]Result, error) {
	// Slice to hold the results
	var dnaMatches []Result

	// Execute the query using GORM's query builder instead of raw SQL
	result := r.db.DB.Table("dna_matches").
		Select("id, match_individual_id").
		Where("link is not null and link != '' and shared_segments_count is null").
		Order("total_shared_segments_length_in_cm desc").
		Offset(offset).
		Limit(limit).
		Scan(&dnaMatches)

	if result.Error != nil {
		return nil, result.Error
	}

	return dnaMatches, nil
}

func (r Repository) saveDnaMatchSharedSegments(segments []DnaSharedSegment, matchId string) error {

	for _, segment := range segments {
		_, err := r.UpsertSharedSegment(segment, matchId)

		if err != nil {
			return err
		}
	}

	return nil
}

func (r *Repository) UpsertSharedSegment(segment DnaSharedSegment, matchId string) (int, error) {
	// Define the shared segment data
	sharedSegment := struct {
		ID                   int     `gorm:"primaryKey;column:id"`
		MatchID              string  `gorm:"column:match_id"`
		ChromosomeID         string  `gorm:"column:chromosome_id"`
		StartPosition        string  `gorm:"column:start_position"`
		EndPosition          string  `gorm:"column:end_position"`
		StartRsid            string  `gorm:"column:start_rsid"`
		EndRsid              string  `gorm:"column:end_rsid"`
		LengthInCentimorgans float64 `gorm:"column:length_in_centimorgans"`
		SnpCount             int     `gorm:"column:snp_count"`
	}{
		MatchID:              matchId,
		ChromosomeID:         segment.ChromosomeID,
		StartPosition:        segment.StartPosition,
		EndPosition:          segment.EndPosition,
		StartRsid:            segment.StartRsid,
		EndRsid:              segment.EndRsid,
		LengthInCentimorgans: segment.LengthInCentimorgans,
		SnpCount:             segment.SnpCount,
	}

	// Try to find an existing record
	var existingSegment struct{ ID int }
	result := r.db.DB.Table("shared_segment").
		Where("match_id = ? AND chromosome_id = ? AND start_position = ? AND end_position = ?",
			matchId, segment.ChromosomeID, segment.StartPosition, segment.EndPosition).
		First(&existingSegment)

	// If the record exists, update it
	if result.Error == nil {
		r.db.DB.Table("shared_segment").
			Where("id = ?", existingSegment.ID).
			Updates(map[string]interface{}{
				"start_rsid":             segment.StartRsid,
				"end_rsid":               segment.EndRsid,
				"length_in_centimorgans": segment.LengthInCentimorgans,
				"snp_count":              segment.SnpCount,
			})
		return existingSegment.ID, nil
	}

	// If the record doesn't exist, create a new one
	result = r.db.DB.Table("shared_segment").Create(&sharedSegment)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to upsert shared segment: %v", result.Error)
	}

	return sharedSegment.ID, nil
}

func (r *Repository) updateSharedSegmentsCount(matchId string, count int) error {
	currentTime := time.Now()

	// Update the record using GORM
	result := r.db.DB.Model(&database.DNAMatch{}).
		Where("id = ?", matchId).
		Updates(map[string]interface{}{
			"shared_segments_count": count,
			"updated_at":            currentTime.Format("2006-01-02 15:04:05"),
		})

	if result.Error != nil {
		return result.Error
	}

	return nil
}
