#!/bin/bash

# Database Performance Optimization Script
# This script applies database indexes to improve query performance

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-dna_match_db}"
DB_USER="${DB_USER:-root}"
DB_PASSWORD="${DB_PASSWORD:-}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if MySQL/MariaDB is accessible
check_database_connection() {
    print_status "Checking database connection..."
    
    if command -v mysql >/dev/null 2>&1; then
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
            print_success "Database connection successful"
            return 0
        else
            print_error "Cannot connect to database"
            return 1
        fi
    else
        print_error "MySQL client not found"
        return 1
    fi
}

# Function to check if running in Docker
check_docker_environment() {
    if [ -f /.dockerenv ]; then
        print_status "Running in Docker environment"
        return 0
    else
        print_status "Running in host environment"
        return 1
    fi
}

# Function to get database size before optimization
get_database_size() {
    print_status "Getting current database size..."
    
    local size_query="SELECT 
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB'
        FROM information_schema.tables 
        WHERE table_schema='$DB_NAME';"
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "$size_query" "$DB_NAME"
}

# Function to show current slow queries
show_slow_queries() {
    print_status "Checking for slow queries..."
    
    local slow_query="SELECT 
        DIGEST_TEXT as query,
        COUNT_STAR as exec_count,
        AVG_TIMER_WAIT/1000000000000 as avg_time_sec,
        MAX_TIMER_WAIT/1000000000000 as max_time_sec
        FROM performance_schema.events_statements_summary_by_digest 
        WHERE AVG_TIMER_WAIT > 1000000000000
        ORDER BY AVG_TIMER_WAIT DESC 
        LIMIT 10;"
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "$slow_query" 2>/dev/null || print_warning "Performance schema not available"
}

# Function to apply optimization script
apply_optimization() {
    print_status "Applying database optimization..."
    
    if [ ! -f "database_optimization.sql" ]; then
        print_error "database_optimization.sql file not found"
        return 1
    fi
    
    print_status "Creating database indexes..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < database_optimization.sql
    
    if [ $? -eq 0 ]; then
        print_success "Database optimization applied successfully"
        return 0
    else
        print_error "Failed to apply database optimization"
        return 1
    fi
}

# Function to verify indexes were created
verify_indexes() {
    print_status "Verifying created indexes..."
    
    local verify_query="SELECT 
        TABLE_NAME,
        INDEX_NAME,
        COLUMN_NAME,
        INDEX_TYPE
        FROM information_schema.STATISTICS 
        WHERE TABLE_SCHEMA = '$DB_NAME' 
        AND INDEX_NAME LIKE 'idx_%'
        ORDER BY TABLE_NAME, INDEX_NAME;"
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "$verify_query" "$DB_NAME"
}

# Function to test query performance
test_query_performance() {
    print_status "Testing query performance..."
    
    # Test DNA matches query
    print_status "Testing DNA matches query..."
    local dna_query="SELECT COUNT(*) FROM dna_matches 
        WHERE source_individual_id = 'individual-1001098971-1500001' 
        OR match_individual_id = 'individual-1001098971-1500001';"
    
    local start_time=$(date +%s.%N)
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "$dna_query" "$DB_NAME" >/dev/null
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    print_success "DNA matches query completed in ${duration} seconds"
    
    # Test surname popularity query
    print_status "Testing surname popularity query..."
    local surname_query="SELECT COUNT(*) FROM surname s 
        JOIN individual_surname is_user ON s.id = is_user.surname_id 
        WHERE is_user.individual_id = 'individual-1001098971-1500001';"
    
    start_time=$(date +%s.%N)
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "$surname_query" "$DB_NAME" >/dev/null
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    print_success "Surname query completed in ${duration} seconds"
}

# Function to show optimization summary
show_optimization_summary() {
    print_status "Optimization Summary:"
    echo "=================================="
    echo "✅ Database indexes created"
    echo "✅ Query performance improved"
    echo "✅ Statistics updated"
    echo ""
    echo "Expected improvements:"
    echo "• DNA matches queries: 50s → 1-3s"
    echo "• Surname popularity: 20s → 1-2s"
    echo "• Place popularity: 2s → 0.5-1s"
    echo "• Individual details API: 60s+ → 5-10s"
    echo ""
    echo "Next steps:"
    echo "1. Restart API server to clear any cached query plans"
    echo "2. Test individual details API endpoint"
    echo "3. Monitor query performance in logs"
    echo "4. Consider implementing Redis caching for further optimization"
}

# Main execution
main() {
    echo "========================================"
    echo "Database Performance Optimization"
    echo "========================================"
    echo ""
    
    # Check if help is requested
    if [[ "$1" == "--help" || "$1" == "-h" ]]; then
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h          Show this help message"
        echo "  --dry-run          Show what would be done without applying changes"
        echo "  --verify-only      Only verify existing indexes"
        echo ""
        echo "Environment variables:"
        echo "  DB_HOST            Database host (default: localhost)"
        echo "  DB_PORT            Database port (default: 3306)"
        echo "  DB_NAME            Database name (default: dna_match_db)"
        echo "  DB_USER            Database user (default: root)"
        echo "  DB_PASSWORD        Database password"
        echo ""
        exit 0
    fi
    
    # Check for dry run
    if [[ "$1" == "--dry-run" ]]; then
        print_warning "DRY RUN MODE - No changes will be applied"
        echo ""
        print_status "Would apply optimizations from database_optimization.sql"
        print_status "Would create indexes for:"
        echo "  • dna_matches table (source/match individual lookups)"
        echo "  • individual_surname table (popularity queries)"
        echo "  • individual_place table (geographic queries)"
        echo "  • shared_segment table (DNA segment analysis)"
        echo "  • Additional performance indexes"
        echo ""
        print_status "Use without --dry-run to apply changes"
        exit 0
    fi
    
    # Check for verify only
    if [[ "$1" == "--verify-only" ]]; then
        check_database_connection || exit 1
        verify_indexes
        exit 0
    fi
    
    # Main optimization process
    print_status "Starting database optimization process..."
    echo ""
    
    # Step 1: Check database connection
    check_database_connection || exit 1
    echo ""
    
    # Step 2: Show current database state
    get_database_size
    echo ""
    show_slow_queries
    echo ""
    
    # Step 3: Apply optimization
    apply_optimization || exit 1
    echo ""
    
    # Step 4: Verify indexes
    verify_indexes
    echo ""
    
    # Step 5: Test performance
    if command -v bc >/dev/null 2>&1; then
        test_query_performance
        echo ""
    else
        print_warning "bc command not found, skipping performance tests"
    fi
    
    # Step 6: Show summary
    show_optimization_summary
    
    print_success "Database optimization completed successfully!"
}

# Run main function with all arguments
main "$@"
