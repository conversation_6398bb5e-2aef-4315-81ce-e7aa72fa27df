package main

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/dmagur/myheritage/internal/database"
	"gorm.io/gorm"
)

func main() {
	log.Println("Starting DNA Clique Scanner...")

	// Parse command line arguments
	args := parseArgs()

	// Initialize database connection
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Create tables if they don't exist
	if err := createTables(db); err != nil {
		log.Fatalf("Failed to create tables: %v", err)
	}

	// Create and start a new clique run
	scanner := NewCliqueScanner(db.GetDB(), args)
	if err := scanner.Run(); err != nil {
		log.Fatalf("Clique scanning failed: %v", err)
	}

	log.Println("DNA Clique Scanner completed successfully!")
}

// ScannerArgs holds command line arguments
type ScannerArgs struct {
	MinCM          float64
	MinSize        int
	MaxIndividuals int
	DryRun         bool
	Verbose        bool
}

// parseArgs parses command line arguments with defaults
func parseArgs() ScannerArgs {
	args := ScannerArgs{
		MinCM:          50.0,  // Default minimum cM
		MinSize:        3,     // Default minimum clique size
		MaxIndividuals: 1000,  // Default max individuals to process
		DryRun:         false, // Default to actually save results
		Verbose:        false, // Default to minimal logging
	}

	// Parse environment variables or command line args
	if val := os.Getenv("MIN_CM"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			args.MinCM = parsed
		}
	}

	if val := os.Getenv("MIN_SIZE"); val != "" {
		if parsed, err := strconv.Atoi(val); err == nil {
			args.MinSize = parsed
		}
	}

	if val := os.Getenv("MAX_INDIVIDUALS"); val != "" {
		if parsed, err := strconv.Atoi(val); err == nil {
			args.MaxIndividuals = parsed
		}
	}

	if val := os.Getenv("DRY_RUN"); val == "true" {
		args.DryRun = true
	}

	if val := os.Getenv("VERBOSE"); val == "true" {
		args.Verbose = true
	}

	return args
}

// initDatabase initializes the database connection
func initDatabase() (*database.GormDB, error) {
	// Use the same connection logic as the API server
	connStr := database.GetConnectionString()
	return database.NewGormDB(connStr)
}

// createTables creates the clique tables if they don't exist
func createTables(db *database.GormDB) error {
	log.Println("Creating clique tables if they don't exist...")

	// Auto-migrate the clique tables
	if err := db.GetDB().AutoMigrate(&database.DNAClique{}, &database.DNACliqueRun{}); err != nil {
		return fmt.Errorf("failed to migrate clique tables: %w", err)
	}

	log.Println("Clique tables ready")
	return nil
}

// CliqueScanner handles the clique scanning process
type CliqueScanner struct {
	db   *gorm.DB
	args ScannerArgs
	run  *database.DNACliqueRun
}

// NewCliqueScanner creates a new clique scanner
func NewCliqueScanner(db *gorm.DB, args ScannerArgs) *CliqueScanner {
	return &CliqueScanner{
		db:   db,
		args: args,
	}
}

// Run executes the clique scanning process
func (s *CliqueScanner) Run() error {
	startTime := time.Now()

	// Create a new run record
	s.run = &database.DNACliqueRun{
		Status:         "running",
		MinCM:          s.args.MinCM,
		MinSize:        s.args.MinSize,
		MaxIndividuals: s.args.MaxIndividuals,
		StartedAt:      startTime,
	}

	if !s.args.DryRun {
		if err := s.db.Create(s.run).Error; err != nil {
			return fmt.Errorf("failed to create run record: %w", err)
		}
		log.Printf("Created run record with ID: %d", s.run.ID)
	}

	// Step 1: Get connected individuals
	log.Printf("Finding individuals with DNA matches >= %.1f cM...", s.args.MinCM)
	individuals, err := s.getConnectedIndividuals()
	if err != nil {
		s.updateRunStatus("failed", fmt.Sprintf("Failed to get individuals: %v", err))
		return fmt.Errorf("failed to get connected individuals: %w", err)
	}

	s.run.TotalIndividuals = len(individuals)
	log.Printf("Found %d individuals with sufficient connections", len(individuals))

	if len(individuals) == 0 {
		s.updateRunStatus("completed", "No individuals found with sufficient connections")
		return nil
	}

	// Step 2: Build adjacency map
	log.Println("Building adjacency map from DNA matches...")
	adjacencyMap, err := s.buildAdjacencyMap(individuals)
	if err != nil {
		s.updateRunStatus("failed", fmt.Sprintf("Failed to build adjacency map: %v", err))
		return fmt.Errorf("failed to build adjacency map: %w", err)
	}

	// Step 3: Find cliques using Bron-Kerbosch algorithm
	log.Println("Finding cliques using Bron-Kerbosch algorithm...")
	cliques := s.findCliques(individuals, adjacencyMap)

	// Step 4: Filter and process cliques
	log.Printf("Processing %d raw cliques...", len(cliques))
	processedCliques, err := s.processCliques(cliques)
	if err != nil {
		s.updateRunStatus("failed", fmt.Sprintf("Failed to process cliques: %v", err))
		return fmt.Errorf("failed to process cliques: %w", err)
	}

	s.run.TotalCliques = len(processedCliques)
	log.Printf("Found %d valid cliques (size >= %d)", len(processedCliques), s.args.MinSize)

	// Step 5: Save cliques to database
	if !s.args.DryRun {
		log.Println("Saving cliques to database...")
		if err := s.saveCliques(processedCliques); err != nil {
			s.updateRunStatus("failed", fmt.Sprintf("Failed to save cliques: %v", err))
			return fmt.Errorf("failed to save cliques: %w", err)
		}
	} else {
		log.Println("DRY RUN: Skipping database save")
		s.printCliqueSummary(processedCliques)
	}

	// Complete the run
	processingTime := time.Since(startTime).Milliseconds()
	s.run.ProcessingTimeMs = processingTime
	s.updateRunStatus("completed", "")

	log.Printf("Clique scanning completed in %d ms", processingTime)
	log.Printf("Summary: %d individuals, %d cliques found", s.run.TotalIndividuals, s.run.TotalCliques)

	return nil
}

// updateRunStatus updates the run status in the database
func (s *CliqueScanner) updateRunStatus(status, errorMsg string) {
	if s.args.DryRun || s.run == nil {
		return
	}

	now := time.Now()
	s.run.Status = status
	s.run.ErrorMessage = errorMsg
	if status == "completed" || status == "failed" {
		s.run.CompletedAt = &now
	}

	if err := s.db.Save(s.run).Error; err != nil {
		log.Printf("Failed to update run status: %v", err)
	}
}

// printCliqueSummary prints a summary of found cliques for dry run
func (s *CliqueScanner) printCliqueSummary(cliques []CliqueResult) {
	log.Println("=== CLIQUE SUMMARY (DRY RUN) ===")
	for i, clique := range cliques {
		if i >= 10 { // Limit output
			log.Printf("... and %d more cliques", len(cliques)-10)
			break
		}
		log.Printf("Clique %d: %d members, avg %.1f cM, members: %v",
			i+1, clique.Size, clique.AvgCM, clique.Members[:min(3, len(clique.Members))])
	}
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
